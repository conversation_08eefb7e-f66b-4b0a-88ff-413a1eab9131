# نظام إدارة الوثائق البسيط
## Simple Document Management System

مشروع بسيط لطلاب السنة الثالثة - مقرر الحوسبة السحابية والأنظمة الموزعة  
الجامعة الإسلامية - غزة

---

## 📋 وصف المشروع

نظام بسيط لإدارة الوثائق يتيح:
- رفع ملفات PDF و Word
- استخراج النص من الملفات
- تصنيف تلقائي للوثائق
- البحث في المحتوى
- عرض إحصائيات بسيطة

## 🛠️ التقنيات المستخدمة

- **PHP 7.4+** - لغة البرمجة الخلفية
- **MySQL 5.7+** - قاعدة البيانات
- **Bootstrap 5** - تصميم الواجهة
- **JavaScript** - التفاعل مع المستخدم
- **Font Awesome** - الأيقونات

## 📁 هيكل المشروع

```
simple-document-system/
├── index.php          # الصفحة الرئيسية
├── config.php         # إعدادات قاعدة البيانات
├── database.sql       # هيكل قاعدة البيانات
├── home.php           # صفحة البداية
├── upload.php         # صفحة رفع الملفات
├── search.php         # صفحة البحث
├── documents.php      # صفحة عرض الوثائق
├── view.php           # صفحة عرض الوثيقة
├── stats.php          # صفحة الإحصائيات
├── uploads/           # مجلد الملفات المرفوعة
└── README_SIMPLE.md   # هذا الملف
```

## 🚀 طريقة التشغيل

### 1. متطلبات النظام
- XAMPP أو WAMP أو LAMP
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث

### 2. خطوات التثبيت

1. **تحميل الملفات**
   ```bash
   # نسخ المشروع إلى مجلد htdocs
   cp -r simple-document-system/ /xampp/htdocs/
   ```

2. **إعداد قاعدة البيانات**
   - افتح phpMyAdmin
   - أنشئ قاعدة بيانات جديدة باسم `simple_docs`
   - استورد ملف `database.sql`

3. **تعديل الإعدادات**
   - افتح ملف `config.php`
   - عدّل بيانات الاتصال بقاعدة البيانات:
   ```php
   $host = 'localhost';
   $dbname = 'simple_docs';
   $username = 'root';
   $password = '';
   ```

4. **تشغيل النظام**
   - ابدأ تشغيل XAMPP
   - افتح المتصفح واذهب إلى: `http://localhost/simple-document-system`

## 📊 الميزات الرئيسية

### 1. رفع الوثائق
- دعم ملفات PDF, DOC, DOCX
- حد أقصى 10 ميجابايت للملف
- استخراج النص تلقائياً
- تصنيف تلقائي للوثائق

### 2. البحث
- البحث في أسماء الملفات
- البحث في محتوى الوثائق
- تمييز النتائج المطابقة
- حفظ إحصائيات البحث

### 3. التصنيف التلقائي
الفئات المدعومة:
- **أكاديمي**: بحث، دراسة، جامعة، أطروحة
- **تجاري**: شركة، أعمال، مبيعات، ربح
- **قانوني**: قانون، عقد، اتفاقية، محكمة
- **تقني**: تقنية، برمجة، نظام، تطوير
- **طبي**: طب، مريض، علاج، دواء
- **عام**: للوثائق غير المصنفة

### 4. الإحصائيات
- عدد الوثائق الإجمالي
- حجم التخزين المستخدم
- عدد البحثات
- توزيع الفئات
- البحثات الشائعة

## 🔧 كيفية عمل النظام

### 1. رفع الملفات
```php
// التحقق من نوع الملف
$allowed_types = ['pdf', 'doc', 'docx'];
if (!in_array($file_extension, $allowed_types)) {
    // رفض الملف
}

// استخراج النص
if ($file_extension == 'pdf') {
    $content = extractTextFromPDF($filepath);
} else {
    $content = extractTextFromWord($filepath);
}

// التصنيف التلقائي
$category = classifyDocument($title, $content);
```

### 2. خوارزمية التصنيف
```php
function classifyDocument($title, $content) {
    $categories = [
        'أكاديمي' => ['بحث', 'دراسة', 'جامعة'],
        'تجاري' => ['شركة', 'أعمال', 'ربح'],
        // ... المزيد
    ];
    
    // حساب النقاط لكل فئة
    foreach ($categories as $category => $keywords) {
        $score = 0;
        foreach ($keywords as $keyword) {
            if (strpos($text, $keyword) !== false) {
                $score++;
            }
        }
        $scores[$category] = $score;
    }
    
    // إرجاع الفئة الأعلى نقاط
    return array_search(max($scores), $scores);
}
```

### 3. البحث
```sql
SELECT * FROM documents 
WHERE title LIKE '%keyword%' 
   OR content LIKE '%keyword%' 
   OR original_name LIKE '%keyword%'
ORDER BY upload_date DESC
```

## 🎯 أهداف التعلم

هذا المشروع يساعد الطلاب على تعلم:

### 1. تطوير الويب
- PHP للبرمجة الخلفية
- MySQL لقواعد البيانات
- HTML/CSS/JavaScript للواجهة
- Bootstrap للتصميم المتجاوب

### 2. إدارة الملفات
- رفع الملفات بأمان
- التحقق من أنواع الملفات
- استخراج النص من الملفات
- تنظيم الملفات في النظام

### 3. قواعد البيانات
- تصميم الجداول
- العلاقات بين الجداول
- الاستعلامات المعقدة
- الفهرسة والأداء

### 4. خوارزميات البحث والتصنيف
- البحث النصي
- تصنيف الوثائق
- معالجة النصوص
- حساب الإحصائيات

## 📈 إمكانيات التطوير

يمكن تطوير المشروع بإضافة:

### المرحلة الثانية
- نظام المستخدمين والصلاحيات
- تحسين خوارزمية التصنيف
- دعم المزيد من أنواع الملفات
- واجهة إدارة متقدمة

### المرحلة الثالثة
- الذكاء الاصطناعي للتصنيف
- البحث الدلالي
- التكامل مع الخدمات السحابية
- تطبيق الهاتف المحمول

## 🔒 الأمان

الإجراءات الأمنية المطبقة:
- التحقق من أنواع الملفات
- تنظيف المدخلات من المستخدم
- استخدام Prepared Statements
- تشفير أسماء الملفات

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من تشغيل MySQL
   - تحقق من بيانات الاتصال في `config.php`

2. **لا يمكن رفع الملفات**
   - تأكد من صلاحيات مجلد `uploads`
   - تحقق من إعدادات PHP للرفع

3. **لا يتم استخراج النص**
   - تأكد من وجود `pdftotext` للملفات PDF
   - تحقق من امتداد ZIP لملفات Word

4. **البحث لا يعمل**
   - تأكد من وجود بيانات في قاعدة البيانات
   - تحقق من ترميز النصوص (UTF-8)

## 📞 الدعم والمساعدة

للحصول على المساعدة:
- راجع التعليقات في الكود
- تحقق من ملفات السجل (logs)
- اطلب المساعدة من المدرس أو الزملاء

## 📝 تقرير المشروع

عند كتابة التقرير، تأكد من تضمين:

### 1. المقدمة
- وصف المشكلة
- أهداف المشروع
- التقنيات المستخدمة

### 2. التصميم
- هيكل قاعدة البيانات
- تصميم الواجهة
- خوارزمية التصنيف

### 3. التطبيق
- خطوات التطوير
- التحديات والحلول
- اختبار النظام

### 4. النتائج
- لقطات شاشة للنظام
- إحصائيات الأداء
- تقييم النتائج

### 5. الخلاصة
- ما تم تعلمه
- إمكانيات التطوير
- التوصيات

## 🎓 معايير التقييم

سيتم تقييم المشروع على أساس:
- **الوظائف (40%)**: عمل جميع الميزات بشكل صحيح
- **التصميم (20%)**: جودة الواجهة وسهولة الاستخدام
- **الكود (20%)**: جودة البرمجة والتعليقات
- **التقرير (20%)**: شمولية ووضوح التقرير

## 📅 الجدول الزمني المقترح

- **الأسبوع 1**: إعداد البيئة وقاعدة البيانات
- **الأسبوع 2**: تطوير رفع الملفات والتصنيف
- **الأسبوع 3**: تطوير البحث والعرض
- **الأسبوع 4**: الإحصائيات والتحسينات
- **الأسبوع 5**: الاختبار وكتابة التقرير

---

**ملاحظة**: هذا مشروع تعليمي مبسط مناسب لطلاب السنة الثالثة. يركز على المفاهيم الأساسية دون تعقيد غير ضروري.

**حظاً موفقاً في مشروعك! 🎓**
