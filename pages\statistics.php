<?php
/**
 * Statistics Page
 * Display comprehensive system statistics and performance metrics
 */

// Initialize performance tracker
$performanceTracker = new PerformanceTracker($db);

// Get system statistics
try {
    $sql = "SELECT stat_name, stat_value FROM system_statistics";
    $stmt = $db->query($sql);
    $systemStats = [];
    while ($row = $stmt->fetch()) {
        $systemStats[$row['stat_name']] = $row['stat_value'];
    }
} catch (Exception $e) {
    $systemStats = [];
}

// Get performance statistics
$performanceStats = $performanceTracker->getSystemStats(30);
$performanceSummary = $performanceTracker->getPerformanceSummary();

// Get classification statistics
$classificationEngine = new ClassificationEngine($db);
$classificationStats = $classificationEngine->getClassificationStatistics();

// Get file type distribution
try {
    $sql = "SELECT file_type, COUNT(*) as count, SUM(file_size) as total_size
            FROM documents 
            WHERE status = 'completed' 
            GROUP BY file_type 
            ORDER BY count DESC";
    $stmt = $db->query($sql);
    $fileTypeStats = $stmt->fetchAll();
} catch (Exception $e) {
    $fileTypeStats = [];
}

// Get upload trends (last 30 days)
try {
    $sql = "SELECT DATE(upload_date) as date, COUNT(*) as uploads, SUM(file_size) as size
            FROM documents 
            WHERE upload_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            AND status = 'completed'
            GROUP BY DATE(upload_date)
            ORDER BY date ASC";
    $stmt = $db->query($sql);
    $uploadTrends = $stmt->fetchAll();
} catch (Exception $e) {
    $uploadTrends = [];
}

// Get search trends
try {
    $sql = "SELECT DATE(search_date) as date, COUNT(*) as searches, AVG(execution_time) as avg_time
            FROM search_history 
            WHERE search_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(search_date)
            ORDER BY date ASC";
    $stmt = $db->query($sql);
    $searchTrends = $stmt->fetchAll();
} catch (Exception $e) {
    $searchTrends = [];
}

// Get top search queries
try {
    $sql = "SELECT search_query, COUNT(*) as frequency, AVG(execution_time) as avg_time
            FROM search_history 
            WHERE search_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY search_query 
            ORDER BY frequency DESC 
            LIMIT 10";
    $stmt = $db->query($sql);
    $topSearches = $stmt->fetchAll();
} catch (Exception $e) {
    $topSearches = [];
}

// Get performance alerts
$performanceAlerts = $performanceTracker->getPerformanceAlerts();

function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-chart-bar"></i> System Statistics
        </h1>
    </div>
</div>

<!-- System Overview -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo number_format($systemStats['total_documents'] ?? 0); ?></div>
            <div class="stat-label">
                <i class="fas fa-file-alt"></i> Total Documents
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #17a2b8, #138496);">
            <div class="stat-number"><?php echo formatFileSize($systemStats['total_file_size'] ?? 0); ?></div>
            <div class="stat-label">
                <i class="fas fa-hdd"></i> Storage Used
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
            <div class="stat-number"><?php echo number_format($systemStats['total_searches'] ?? 0); ?></div>
            <div class="stat-label">
                <i class="fas fa-search"></i> Total Searches
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #6f42c1, #5a32a3);">
            <div class="stat-number"><?php echo number_format($systemStats['total_classifications'] ?? 0); ?></div>
            <div class="stat-label">
                <i class="fas fa-tags"></i> Classifications
            </div>
        </div>
    </div>
</div>

<!-- Performance Alerts -->
<?php if (!empty($performanceAlerts)): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Performance Alerts
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($performanceAlerts as $alert): ?>
                <div class="alert alert-<?php echo $alert['severity'] === 'warning' ? 'warning' : 'danger'; ?> mb-2">
                    <strong><?php echo ucfirst($alert['type']); ?>:</strong> <?php echo $alert['message']; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row">
    <!-- File Type Distribution -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> File Type Distribution
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($fileTypeStats)): ?>
                    <p class="text-muted text-center">No data available</p>
                <?php else: ?>
                    <canvas id="fileTypeChart" width="400" height="200"></canvas>
                    <div class="mt-3">
                        <?php foreach ($fileTypeStats as $stat): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>
                                <span class="badge bg-secondary"><?php echo strtoupper($stat['file_type']); ?></span>
                                <?php echo $stat['count']; ?> files
                            </span>
                            <span class="text-muted"><?php echo formatFileSize($stat['total_size']); ?></span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Classification Distribution -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags"></i> Classification Distribution
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($classificationStats)): ?>
                    <p class="text-muted text-center">No classified documents</p>
                <?php else: ?>
                    <canvas id="classificationChart" width="400" height="200"></canvas>
                    <div class="mt-3">
                        <?php foreach ($classificationStats as $stat): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>
                                <span class="badge classification-<?php echo $stat['classification']; ?>">
                                    <?php echo ucfirst($stat['classification']); ?>
                                </span>
                                <?php echo $stat['count']; ?> docs
                            </span>
                            <span class="text-muted">
                                Avg: <?php echo number_format($stat['avg_confidence'], 1); ?>%
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Upload Trends -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> Upload Trends (Last 30 Days)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($uploadTrends)): ?>
                    <p class="text-muted text-center">No upload data available</p>
                <?php else: ?>
                    <canvas id="uploadTrendsChart" width="400" height="200"></canvas>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Performance Summary -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt"></i> Performance Summary
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($performanceStats)): ?>
                    <?php foreach ($performanceStats as $stat): ?>
                    <div class="mb-3">
                        <h6 class="text-capitalize"><?php echo str_replace('_', ' ', $stat['operation_type']); ?></h6>
                        <div class="small">
                            <div>Operations: <?php echo number_format($stat['total_operations']); ?></div>
                            <div>Avg Time: <?php echo PerformanceTracker::formatExecutionTime($stat['avg_execution_time']); ?></div>
                            <div>Records: <?php echo number_format($stat['total_records']); ?></div>
                            <div>Memory: <?php echo PerformanceTracker::formatMemoryUsage($stat['avg_memory_usage']); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted">No performance data available</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Search Trends -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search"></i> Search Activity (Last 30 Days)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($searchTrends)): ?>
                    <p class="text-muted text-center">No search data available</p>
                <?php else: ?>
                    <canvas id="searchTrendsChart" width="400" height="200"></canvas>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Top Searches -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-fire"></i> Top Search Queries
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($topSearches)): ?>
                    <p class="text-muted text-center">No search data available</p>
                <?php else: ?>
                    <?php foreach ($topSearches as $search): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                        <div>
                            <div class="fw-bold">
                                <?php echo htmlspecialchars($search['search_query']); ?>
                            </div>
                            <small class="text-muted">
                                Avg: <?php echo PerformanceTracker::formatExecutionTime($search['avg_time']); ?>
                            </small>
                        </div>
                        <span class="badge bg-primary">
                            <?php echo $search['frequency']; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download"></i> Export Data
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="api/export_performance.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-chart-line"></i> Performance Data
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="api/export_documents.php" class="btn btn-outline-success w-100">
                            <i class="fas fa-file-alt"></i> Document List
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="api/export_searches.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-search"></i> Search History
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="api/export_classifications.php" class="btn btn-outline-warning w-100">
                            <i class="fas fa-tags"></i> Classifications
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// File Type Chart
<?php if (!empty($fileTypeStats)): ?>
const fileTypeCtx = document.getElementById('fileTypeChart').getContext('2d');
new Chart(fileTypeCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode(array_map('strtoupper', array_column($fileTypeStats, 'file_type'))); ?>,
        datasets: [{
            data: <?php echo json_encode(array_column($fileTypeStats, 'count')); ?>,
            backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
<?php endif; ?>

// Classification Chart
<?php if (!empty($classificationStats)): ?>
const classificationCtx = document.getElementById('classificationChart').getContext('2d');
new Chart(classificationCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode(array_map('ucfirst', array_column($classificationStats, 'classification'))); ?>,
        datasets: [{
            data: <?php echo json_encode(array_column($classificationStats, 'count')); ?>,
            backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#17a2b8']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
<?php endif; ?>

// Upload Trends Chart
<?php if (!empty($uploadTrends)): ?>
const uploadTrendsCtx = document.getElementById('uploadTrendsChart').getContext('2d');
new Chart(uploadTrendsCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($uploadTrends, 'date')); ?>,
        datasets: [{
            label: 'Documents Uploaded',
            data: <?php echo json_encode(array_column($uploadTrends, 'uploads')); ?>,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
<?php endif; ?>

// Search Trends Chart
<?php if (!empty($searchTrends)): ?>
const searchTrendsCtx = document.getElementById('searchTrendsChart').getContext('2d');
new Chart(searchTrendsCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($searchTrends, 'date')); ?>,
        datasets: [{
            label: 'Searches',
            data: <?php echo json_encode(array_column($searchTrends, 'searches')); ?>,
            borderColor: '#ffc107',
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
<?php endif; ?>
</script>
