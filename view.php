<?php
/**
 * صفحة عرض الوثيقة
 */

$doc_id = $_GET['id'] ?? 0;

if (!$doc_id) {
    header('Location: index.php?page=documents');
    exit;
}

// جلب بيانات الوثيقة
$stmt = $pdo->prepare("SELECT * FROM documents WHERE id = ?");
$stmt->execute([$doc_id]);
$document = $stmt->fetch();

if (!$document) {
    header('Location: index.php?page=documents');
    exit;
}

// جلب الوثائق ذات الصلة (نفس الفئة)
$stmt = $pdo->prepare("SELECT * FROM documents WHERE category = ? AND id != ? ORDER BY upload_date DESC LIMIT 5");
$stmt->execute([$document['category'], $doc_id]);
$related_docs = $stmt->fetchAll();
?>

<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="index.php?page=documents">الوثائق</a></li>
                <li class="breadcrumb-item active"><?php echo htmlspecialchars($document['title']); ?></li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <!-- محتوى الوثيقة -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h4 class="mb-1"><?php echo htmlspecialchars($document['title']); ?></h4>
                        <small class="text-muted">
                            <i class="fas fa-file"></i> <?php echo htmlspecialchars($document['original_name']); ?>
                        </small>
                    </div>
                    <div>
                        <span class="category-badge category-<?php echo $document['category']; ?>">
                            <?php echo $document['category']; ?>
                        </span>
                        <span class="badge bg-secondary ms-2">
                            <?php echo strtoupper($document['file_type']); ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- معلومات الملف -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <strong>حجم الملف:</strong><br>
                        <?php echo formatFileSize($document['file_size']); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>نوع الملف:</strong><br>
                        <?php echo strtoupper($document['file_type']); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ الرفع:</strong><br>
                        <?php echo date('Y-m-d H:i', strtotime($document['upload_date'])); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>الفئة:</strong><br>
                        <?php echo $document['category']; ?>
                    </div>
                </div>
                
                <hr>
                
                <!-- محتوى الوثيقة -->
                <h6>محتوى الوثيقة:</h6>
                <?php if (!empty($document['content'])): ?>
                    <div class="border rounded p-3 bg-light" style="max-height: 400px; overflow-y: auto;">
                        <pre style="white-space: pre-wrap; font-family: inherit; margin: 0;">
<?php echo htmlspecialchars($document['content']); ?>
                        </pre>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        لم يتم استخراج محتوى نصي من هذا الملف.
                    </div>
                <?php endif; ?>
                
                <!-- أزرار الإجراءات -->
                <div class="mt-4">
                    <a href="<?php echo UPLOAD_DIR . $document['filename']; ?>" 
                       class="btn btn-primary" 
                       download="<?php echo $document['original_name']; ?>">
                        <i class="fas fa-download"></i> تحميل الملف
                    </a>
                    <a href="index.php?page=search&q=<?php echo urlencode($document['category']); ?>" 
                       class="btn btn-outline-info">
                        <i class="fas fa-search"></i> البحث عن مشابه
                    </a>
                    <a href="index.php?page=documents" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- معلومات إضافية -->
        <div class="card mb-3">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> معلومات الملف</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>المعرف:</strong></td>
                        <td><?php echo $document['id']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>اسم الملف:</strong></td>
                        <td><?php echo htmlspecialchars($document['filename']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>الاسم الأصلي:</strong></td>
                        <td><?php echo htmlspecialchars($document['original_name']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>حجم الملف:</strong></td>
                        <td><?php echo formatFileSize($document['file_size']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>نوع الملف:</strong></td>
                        <td><?php echo strtoupper($document['file_type']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>الفئة:</strong></td>
                        <td>
                            <span class="category-badge category-<?php echo $document['category']; ?>">
                                <?php echo $document['category']; ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الرفع:</strong></td>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($document['upload_date'])); ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- الوثائق ذات الصلة -->
        <?php if (!empty($related_docs)): ?>
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-link"></i> وثائق مشابهة</h5>
            </div>
            <div class="card-body">
                <?php foreach ($related_docs as $related): ?>
                    <div class="mb-3 pb-2 border-bottom">
                        <div class="fw-bold">
                            <a href="index.php?page=view&id=<?php echo $related['id']; ?>" class="text-decoration-none">
                                <?php echo htmlspecialchars(substr($related['title'], 0, 40)); ?>
                                <?php if (strlen($related['title']) > 40) echo '...'; ?>
                            </a>
                        </div>
                        <small class="text-muted">
                            <span class="badge bg-secondary"><?php echo strtoupper($related['file_type']); ?></span>
                            <?php echo formatFileSize($related['file_size']); ?>
                            | <?php echo date('Y-m-d', strtotime($related['upload_date'])); ?>
                        </small>
                    </div>
                <?php endforeach; ?>
                
                <div class="text-center">
                    <a href="index.php?page=documents&category=<?php echo urlencode($document['category']); ?>" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-list"></i> عرض جميع وثائق <?php echo $document['category']; ?>
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// إضافة وظائف تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تمييز النص عند النقر
    const contentDiv = document.querySelector('pre');
    if (contentDiv) {
        contentDiv.addEventListener('click', function() {
            if (window.getSelection) {
                const selection = window.getSelection();
                const range = document.createRange();
                range.selectNodeContents(this);
                selection.removeAllRanges();
                selection.addRange(range);
            }
        });
    }
});
</script>
