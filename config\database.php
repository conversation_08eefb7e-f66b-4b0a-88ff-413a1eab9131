<?php
/**
 * Database Configuration
 * Cloud-Based Document Analytics System
 * 
 * This file contains database connection settings and configuration
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'document_analytics';
    private $username = 'root';
    private $password = '';
    private $conn;
    
    /**
     * Get database connection
     * @return PDO|null
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
    
    /**
     * Close database connection
     */
    public function closeConnection() {
        $this->conn = null;
    }
}

/**
 * Application Configuration
 */
class Config {
    // File upload settings
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    const ALLOWED_EXTENSIONS = ['pdf', 'doc', 'docx'];
    const UPLOAD_PATH = 'uploads/documents/';
    
    // Search settings
    const SEARCH_RESULTS_PER_PAGE = 10;
    const HIGHLIGHT_CLASS = 'search-highlight';
    
    // Classification categories
    const CLASSIFICATION_CATEGORIES = [
        'academic' => 'Academic Papers',
        'business' => 'Business Documents',
        'legal' => 'Legal Documents',
        'technical' => 'Technical Documentation',
        'medical' => 'Medical Documents',
        'other' => 'Other Documents'
    ];
    
    // Performance tracking
    const ENABLE_PERFORMANCE_TRACKING = true;
    
    /**
     * Get upload directory path
     * @return string
     */
    public static function getUploadPath() {
        $path = self::UPLOAD_PATH;
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }
        return $path;
    }
}
?>
