# Docker Compose configuration for Document Analytics System
version: '3.8'

services:
  # Web Application
  web:
    build: 
      context: ..
      dockerfile: deploy/Dockerfile
    container_name: document_analytics_web
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../:/var/www/html
      - uploads_data:/var/www/html/uploads
      - ./nginx.conf:/etc/nginx/sites-available/default
    environment:
      - DB_HOST=database
      - DB_NAME=document_analytics
      - DB_USER=doc_user
      - DB_PASS=secure_password_123
      - ENVIRONMENT=production
    depends_on:
      - database
      - redis
    networks:
      - app_network
    restart: unless-stopped

  # MySQL Database
  database:
    image: mysql:8.0
    container_name: document_analytics_db
    environment:
      MYSQL_ROOT_PASSWORD: root_password_123
      MYSQL_DATABASE: document_analytics
      MYSQL_USER: doc_user
      MYSQL_PASSWORD: secure_password_123
    volumes:
      - db_data:/var/lib/mysql
      - ../database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    ports:
      - "3306:3306"
    networks:
      - app_network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis for Caching
  redis:
    image: redis:7-alpine
    container_name: document_analytics_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network
    restart: unless-stopped

  # Nginx Load Balancer (for scaling)
  nginx:
    image: nginx:alpine
    container_name: document_analytics_nginx
    ports:
      - "8080:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - web
    networks:
      - app_network
    restart: unless-stopped

  # Elasticsearch for Advanced Search (Optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: document_analytics_search
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - es_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - app_network
    restart: unless-stopped

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus
    container_name: document_analytics_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - app_network
    restart: unless-stopped

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana
    container_name: document_analytics_grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - app_network
    restart: unless-stopped

volumes:
  db_data:
    driver: local
  uploads_data:
    driver: local
  redis_data:
    driver: local
  es_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  app_network:
    driver: bridge
