<?php
/**
 * الصفحة الرئيسية
 */

// جلب إحصائيات بسيطة
$stmt = $pdo->query("SELECT COUNT(*) as total_docs FROM documents");
$total_docs = $stmt->fetch()['total_docs'];

$stmt = $pdo->query("SELECT COUNT(*) as total_searches FROM searches");
$total_searches = $stmt->fetch()['total_searches'];

$stmt = $pdo->query("SELECT SUM(file_size) as total_size FROM documents");
$total_size = $stmt->fetch()['total_size'] ?? 0;

// جلب آخر الوثائق المرفوعة
$stmt = $pdo->query("SELECT * FROM documents ORDER BY upload_date DESC LIMIT 5");
$recent_docs = $stmt->fetchAll();
?>

<div class="row">
    <div class="col-12">
        <h1 class="text-center mb-4">
            <i class="fas fa-file-alt text-primary"></i>
            مرحباً بك في نظام إدارة الوثائق البسيط
        </h1>
        <p class="text-center text-muted mb-5">
            نظام بسيط لرفع وتصنيف والبحث في الوثائق باستخدام PHP و MySQL
        </p>
    </div>
</div>

<!-- بطاقات الإحصائيات -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <div class="stats-number"><?php echo $total_docs; ?></div>
            <div>إجمالي الوثائق</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(45deg, #17a2b8, #138496);">
            <div class="stats-number"><?php echo formatFileSize($total_size); ?></div>
            <div>حجم التخزين</div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(45deg, #ffc107, #e0a800);">
            <div class="stats-number"><?php echo $total_searches; ?></div>
            <div>عدد البحثات</div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> الإجراءات السريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="index.php?page=upload" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-upload fa-2x mb-2"></i><br>
                            رفع وثيقة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="index.php?page=search" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-search fa-2x mb-2"></i><br>
                            البحث في الوثائق
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="index.php?page=documents" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-list fa-2x mb-2"></i><br>
                            عرض جميع الوثائق
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="index.php?page=stats" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                            الإحصائيات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- آخر الوثائق المرفوعة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> آخر الوثائق المرفوعة</h5>
            </div>
            <div class="card-body">
                <?php if (empty($recent_docs)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد وثائق مرفوعة بعد</p>
                        <a href="index.php?page=upload" class="btn btn-primary">
                            <i class="fas fa-upload"></i> ارفع أول وثيقة
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach ($recent_docs as $doc): ?>
                        <div class="document-item">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1">
                                        <a href="index.php?page=view&id=<?php echo $doc['id']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($doc['title']); ?>
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-file"></i> <?php echo htmlspecialchars($doc['original_name']); ?>
                                        | <i class="fas fa-hdd"></i> <?php echo formatFileSize($doc['file_size']); ?>
                                        | <i class="fas fa-calendar"></i> <?php echo date('Y-m-d H:i', strtotime($doc['upload_date'])); ?>
                                    </small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <span class="category-badge category-<?php echo $doc['category']; ?>">
                                        <?php echo $doc['category']; ?>
                                    </span>
                                    <span class="badge bg-secondary ms-2">
                                        <?php echo strtoupper($doc['file_type']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="text-center mt-3">
                        <a href="index.php?page=documents" class="btn btn-outline-primary">
                            <i class="fas fa-list"></i> عرض جميع الوثائق
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- معلومات المشروع -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> حول المشروع</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الميزات الرئيسية:</h6>
                        <ul>
                            <li>رفع ملفات PDF و Word</li>
                            <li>استخراج النص من الملفات</li>
                            <li>تصنيف تلقائي للوثائق</li>
                            <li>البحث في المحتوى</li>
                            <li>إحصائيات بسيطة</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>التقنيات المستخدمة:</h6>
                        <ul>
                            <li>PHP 7.4+</li>
                            <li>MySQL 5.7+</li>
                            <li>Bootstrap 5</li>
                            <li>Font Awesome</li>
                            <li>JavaScript</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
