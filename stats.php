<?php
/**
 * صفحة الإحصائيات
 */

// إحصائيات عامة
$stmt = $pdo->query("SELECT COUNT(*) as total_docs FROM documents");
$total_docs = $stmt->fetch()['total_docs'];

$stmt = $pdo->query("SELECT SUM(file_size) as total_size FROM documents");
$total_size = $stmt->fetch()['total_size'] ?? 0;

$stmt = $pdo->query("SELECT COUNT(*) as total_searches FROM searches");
$total_searches = $stmt->fetch()['total_searches'];

// إحصائيات الفئات
$stmt = $pdo->query("SELECT category, COUNT(*) as count FROM documents GROUP BY category ORDER BY count DESC");
$category_stats = $stmt->fetchAll();

// إحصائيات أنواع الملفات
$stmt = $pdo->query("SELECT file_type, COUNT(*) as count, SUM(file_size) as total_size FROM documents GROUP BY file_type ORDER BY count DESC");
$file_type_stats = $stmt->fetchAll();

// إحصائيات الرفع حسب التاريخ (آخر 7 أيام)
$stmt = $pdo->query("
    SELECT DATE(upload_date) as date, COUNT(*) as count 
    FROM documents 
    WHERE upload_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY DATE(upload_date) 
    ORDER BY date ASC
");
$daily_uploads = $stmt->fetchAll();

// أكثر البحثات شيوعاً
$stmt = $pdo->query("
    SELECT search_term, COUNT(*) as count 
    FROM searches 
    GROUP BY search_term 
    ORDER BY count DESC 
    LIMIT 10
");
$popular_searches = $stmt->fetchAll();

// متوسط حجم الملفات
$stmt = $pdo->query("SELECT AVG(file_size) as avg_size FROM documents");
$avg_file_size = $stmt->fetch()['avg_size'] ?? 0;
?>

<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-chart-bar"></i> إحصائيات النظام</h2>
        <p class="text-muted">نظرة شاملة على أداء النظام والبيانات</p>
    </div>
</div>

<!-- الإحصائيات العامة -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card">
            <div class="stats-number"><?php echo $total_docs; ?></div>
            <div>إجمالي الوثائق</div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, #17a2b8, #138496);">
            <div class="stats-number"><?php echo formatFileSize($total_size); ?></div>
            <div>إجمالي التخزين</div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, #ffc107, #e0a800);">
            <div class="stats-number"><?php echo $total_searches; ?></div>
            <div>إجمالي البحثات</div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, #6f42c1, #5a32a3);">
            <div class="stats-number"><?php echo formatFileSize($avg_file_size); ?></div>
            <div>متوسط حجم الملف</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- إحصائيات الفئات -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> توزيع الفئات</h5>
            </div>
            <div class="card-body">
                <?php if (empty($category_stats)): ?>
                    <p class="text-muted text-center">لا توجد بيانات</p>
                <?php else: ?>
                    <?php 
                    $total_for_percentage = array_sum(array_column($category_stats, 'count'));
                    foreach ($category_stats as $stat): 
                        $percentage = $total_for_percentage > 0 ? ($stat['count'] / $total_for_percentage) * 100 : 0;
                    ?>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="category-badge category-<?php echo $stat['category']; ?>">
                                    <?php echo $stat['category']; ?>
                                </span>
                                <small class="text-muted">
                                    <?php echo $stat['count']; ?> (<?php echo number_format($percentage, 1); ?>%)
                                </small>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" 
                                     style="width: <?php echo $percentage; ?>%"
                                     aria-valuenow="<?php echo $percentage; ?>" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات أنواع الملفات -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-file-alt"></i> أنواع الملفات</h5>
            </div>
            <div class="card-body">
                <?php if (empty($file_type_stats)): ?>
                    <p class="text-muted text-center">لا توجد بيانات</p>
                <?php else: ?>
                    <?php foreach ($file_type_stats as $stat): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                            <div>
                                <h6 class="mb-1">
                                    <span class="badge bg-secondary"><?php echo strtoupper($stat['file_type']); ?></span>
                                </h6>
                                <small class="text-muted">
                                    <?php echo $stat['count']; ?> ملف | 
                                    <?php echo formatFileSize($stat['total_size']); ?>
                                </small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold"><?php echo $stat['count']; ?></div>
                                <small class="text-muted">ملف</small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الرفع اليومي -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> الرفع اليومي (آخر 7 أيام)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($daily_uploads)): ?>
                    <p class="text-muted text-center">لا توجد بيانات للأيام السابقة</p>
                <?php else: ?>
                    <canvas id="dailyUploadsChart" width="400" height="200"></canvas>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- البحثات الشائعة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-fire"></i> البحثات الشائعة</h5>
            </div>
            <div class="card-body">
                <?php if (empty($popular_searches)): ?>
                    <p class="text-muted text-center">لا توجد بحثات بعد</p>
                <?php else: ?>
                    <?php foreach ($popular_searches as $search): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                            <div>
                                <div class="fw-bold">
                                    <a href="index.php?page=search&q=<?php echo urlencode($search['search_term']); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($search['search_term']); ?>
                                    </a>
                                </div>
                            </div>
                            <span class="badge bg-primary">
                                <?php echo $search['count']; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- معلومات النظام -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-server"></i> معلومات النظام</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>إصدار PHP:</h6>
                        <p><?php echo PHP_VERSION; ?></p>
                    </div>
                    <div class="col-md-3">
                        <h6>حد رفع الملفات:</h6>
                        <p><?php echo formatFileSize(MAX_FILE_SIZE); ?></p>
                    </div>
                    <div class="col-md-3">
                        <h6>الأنواع المدعومة:</h6>
                        <p><?php echo implode(', ', ALLOWED_TYPES); ?></p>
                    </div>
                    <div class="col-md-3">
                        <h6>مجلد التخزين:</h6>
                        <p><?php echo UPLOAD_DIR; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// رسم بياني للرفع اليومي
<?php if (!empty($daily_uploads)): ?>
const ctx = document.getElementById('dailyUploadsChart').getContext('2d');
new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($daily_uploads, 'date')); ?>,
        datasets: [{
            label: 'عدد الملفات المرفوعة',
            data: <?php echo json_encode(array_column($daily_uploads, 'count')); ?>,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
<?php endif; ?>

// تحديث الصفحة كل 5 دقائق
setTimeout(function() {
    location.reload();
}, 300000);
</script>
