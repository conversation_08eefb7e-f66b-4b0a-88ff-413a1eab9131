<?php
/**
 * Classification Management Page
 * Manage document classification rules and view classification results
 */

$message = '';
$messageType = '';

// Initialize classification engine
$classificationEngine = new ClassificationEngine($db);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_rule':
                $result = $classificationEngine->addClassificationRule(
                    $_POST['category'],
                    $_POST['keywords'],
                    (float)$_POST['weight'],
                    $_POST['rule_type']
                );
                if ($result) {
                    $message = 'Classification rule added successfully.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to add classification rule.';
                    $messageType = 'danger';
                }
                break;
                
            case 'update_rule':
                $result = $classificationEngine->updateClassificationRule(
                    (int)$_POST['rule_id'],
                    [
                        'category' => $_POST['category'],
                        'keywords' => $_POST['keywords'],
                        'weight' => (float)$_POST['weight'],
                        'rule_type' => $_POST['rule_type'],
                        'is_active' => isset($_POST['is_active'])
                    ]
                );
                if ($result) {
                    $message = 'Classification rule updated successfully.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to update classification rule.';
                    $messageType = 'danger';
                }
                break;
                
            case 'delete_rule':
                $result = $classificationEngine->deleteClassificationRule((int)$_POST['rule_id']);
                if ($result) {
                    $message = 'Classification rule deleted successfully.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to delete classification rule.';
                    $messageType = 'danger';
                }
                break;
                
            case 'reclassify_all':
                $results = $classificationEngine->reclassifyAllDocuments();
                $successCount = count(array_filter($results, function($r) { return $r['category'] !== 'other'; }));
                $message = "Reclassified all documents. $successCount documents successfully classified.";
                $messageType = 'success';
                break;
        }
    }
}

// Get classification rules
$classificationRules = $classificationEngine->getClassificationRules();

// Get classification statistics
$classificationStats = $classificationEngine->getClassificationStatistics();

// Get recent classifications
try {
    $sql = "SELECT d.id, d.title, d.classification, d.classification_confidence, d.upload_date
            FROM documents d 
            WHERE d.status = 'completed' AND d.classification != 'other'
            ORDER BY d.upload_date DESC 
            LIMIT 10";
    $stmt = $db->query($sql);
    $recentClassifications = $stmt->fetchAll();
} catch (Exception $e) {
    $recentClassifications = [];
}
?>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tags"></i> Document Classification
        </h1>
    </div>
</div>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <!-- Classification Statistics -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> Classification Statistics
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($classificationStats)): ?>
                    <p class="text-muted text-center">No classified documents yet.</p>
                <?php else: ?>
                    <?php foreach ($classificationStats as $stat): ?>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="badge classification-<?php echo $stat['classification']; ?>">
                                <?php echo ucfirst($stat['classification']); ?>
                            </span>
                            <small class="text-muted">
                                <?php echo $stat['count']; ?> docs
                            </small>
                        </div>
                        <div class="small text-muted">
                            Avg Confidence: <?php echo number_format($stat['avg_confidence'], 1); ?>%
                            (<?php echo number_format($stat['min_confidence'], 1); ?>% - <?php echo number_format($stat['max_confidence'], 1); ?>%)
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <div class="mt-3">
                    <form method="POST" onsubmit="return confirm('This will reclassify all documents. Continue?');">
                        <input type="hidden" name="action" value="reclassify_all">
                        <button type="submit" class="btn btn-warning btn-sm w-100">
                            <i class="fas fa-sync"></i> Reclassify All Documents
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Recent Classifications -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Recent Classifications
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentClassifications)): ?>
                    <p class="text-muted text-center">No recent classifications.</p>
                <?php else: ?>
                    <?php foreach ($recentClassifications as $doc): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                        <div>
                            <div class="fw-bold">
                                <a href="index.php?action=view_document&id=<?php echo $doc['id']; ?>">
                                    <?php echo htmlspecialchars(substr($doc['title'], 0, 30)); ?>
                                    <?php if (strlen($doc['title']) > 30) echo '...'; ?>
                                </a>
                            </div>
                            <span class="badge classification-<?php echo $doc['classification']; ?>">
                                <?php echo ucfirst($doc['classification']); ?>
                            </span>
                            <small class="text-muted">
                                (<?php echo number_format($doc['classification_confidence'], 1); ?>%)
                            </small>
                        </div>
                        <small class="text-muted">
                            <?php echo date('M j', strtotime($doc['upload_date'])); ?>
                        </small>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Classification Rules Management -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> Classification Rules
                </h5>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addRuleModal">
                    <i class="fas fa-plus"></i> Add Rule
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($classificationRules)): ?>
                    <p class="text-muted text-center">No classification rules defined.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Type</th>
                                    <th>Keywords</th>
                                    <th>Weight</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($classificationRules as $rule): ?>
                                <tr>
                                    <td>
                                        <span class="badge classification-<?php echo $rule['category']; ?>">
                                            <?php echo ucfirst($rule['category']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo ucfirst($rule['rule_type']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small>
                                            <?php echo htmlspecialchars(substr($rule['keywords'], 0, 50)); ?>
                                            <?php if (strlen($rule['keywords']) > 50) echo '...'; ?>
                                        </small>
                                    </td>
                                    <td><?php echo $rule['weight']; ?></td>
                                    <td>
                                        <?php if ($rule['is_active']): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-primary" 
                                                    onclick="editRule(<?php echo htmlspecialchars(json_encode($rule)); ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="POST" style="display: inline;" 
                                                  onsubmit="return confirm('Delete this rule?');">
                                                <input type="hidden" name="action" value="delete_rule">
                                                <input type="hidden" name="rule_id" value="<?php echo $rule['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Classification Algorithm Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Classification Algorithm
                </h5>
            </div>
            <div class="card-body">
                <h6>How Classification Works:</h6>
                <ol>
                    <li><strong>Keyword Matching:</strong> Documents are analyzed for predefined keywords in each category</li>
                    <li><strong>Title Analysis:</strong> Document titles are examined for classification patterns</li>
                    <li><strong>Pattern Recognition:</strong> Text patterns specific to document types are identified</li>
                    <li><strong>Weighted Scoring:</strong> Each method contributes to a final confidence score</li>
                    <li><strong>Threshold Application:</strong> Documents below 30% confidence are marked as "other"</li>
                </ol>
                
                <h6 class="mt-3">Classification Categories:</h6>
                <div class="row">
                    <?php foreach (Config::CLASSIFICATION_CATEGORIES as $key => $name): ?>
                    <div class="col-md-6 mb-2">
                        <span class="badge classification-<?php echo $key; ?> me-2">
                            <?php echo $name; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Rule Modal -->
<div class="modal fade" id="addRuleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Classification Rule</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_rule">
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select name="category" id="category" class="form-select" required>
                            <?php foreach (Config::CLASSIFICATION_CATEGORIES as $key => $name): ?>
                            <option value="<?php echo $key; ?>"><?php echo $name; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="rule_type" class="form-label">Rule Type</label>
                        <select name="rule_type" id="rule_type" class="form-select" required>
                            <option value="keyword">Keyword</option>
                            <option value="title">Title Pattern</option>
                            <option value="pattern">Text Pattern</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="keywords" class="form-label">Keywords/Patterns</label>
                        <textarea name="keywords" id="keywords" class="form-control" rows="3" required
                                  placeholder="Enter comma-separated keywords or patterns"></textarea>
                        <div class="form-text">
                            Separate multiple keywords with commas. For patterns, use simple text patterns.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="weight" class="form-label">Weight</label>
                        <input type="number" name="weight" id="weight" class="form-control" 
                               min="0.1" max="10" step="0.1" value="1.0" required>
                        <div class="form-text">
                            Higher weights give more importance to this rule (0.1 - 10.0).
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Rule</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Rule Modal -->
<div class="modal fade" id="editRuleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Classification Rule</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editRuleForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_rule">
                    <input type="hidden" name="rule_id" id="edit_rule_id">
                    
                    <div class="mb-3">
                        <label for="edit_category" class="form-label">Category</label>
                        <select name="category" id="edit_category" class="form-select" required>
                            <?php foreach (Config::CLASSIFICATION_CATEGORIES as $key => $name): ?>
                            <option value="<?php echo $key; ?>"><?php echo $name; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_rule_type" class="form-label">Rule Type</label>
                        <select name="rule_type" id="edit_rule_type" class="form-select" required>
                            <option value="keyword">Keyword</option>
                            <option value="title">Title Pattern</option>
                            <option value="pattern">Text Pattern</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_keywords" class="form-label">Keywords/Patterns</label>
                        <textarea name="keywords" id="edit_keywords" class="form-control" rows="3" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_weight" class="form-label">Weight</label>
                        <input type="number" name="weight" id="edit_weight" class="form-control" 
                               min="0.1" max="10" step="0.1" required>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" id="edit_is_active" class="form-check-input">
                            <label for="edit_is_active" class="form-check-label">Active</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Rule</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editRule(rule) {
    document.getElementById('edit_rule_id').value = rule.id;
    document.getElementById('edit_category').value = rule.category;
    document.getElementById('edit_rule_type').value = rule.rule_type;
    document.getElementById('edit_keywords').value = rule.keywords;
    document.getElementById('edit_weight').value = rule.weight;
    document.getElementById('edit_is_active').checked = rule.is_active == 1;
    
    new bootstrap.Modal(document.getElementById('editRuleModal')).show();
}
</script>
