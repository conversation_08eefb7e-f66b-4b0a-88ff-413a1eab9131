# Deployment Guide - Cloud-Based Document Analytics System

This guide provides comprehensive instructions for deploying the Document Analytics System to various cloud platforms and hosting environments.

## 🚀 Quick Start

### Local Development Setup

1. **Prerequisites**
   ```bash
   # Required software
   - PHP 7.4+ with extensions: PDO, ZIP, GD
   - MySQL 5.7+ or MariaDB 10.3+
   - Apache or Nginx web server
   - Composer (optional)
   ```

2. **Installation**
   ```bash
   # Clone the repository
   git clone https://github.com/yourusername/document-analytics-system.git
   cd document-analytics-system
   
   # Set up database
   mysql -u root -p < database/schema.sql
   
   # Configure database connection
   cp config/database.php.example config/database.php
   # Edit database credentials in config/database.php
   
   # Set permissions
   chmod 755 uploads/
   chmod 755 uploads/documents/
   ```

3. **Web Server Configuration**
   - Point document root to the project directory
   - Ensure `.htaccess` is enabled (Apache) or configure URL rewriting (Nginx)

## ☁️ Cloud Deployment Options

### 1. AWS Deployment (Recommended)

#### Automated Deployment
```bash
# Make deployment script executable
chmod +x deploy/aws-deploy.sh

# Run automated deployment
./deploy/aws-deploy.sh
```

#### Manual AWS Setup

**Step 1: Create RDS Database**
```bash
# Create RDS MySQL instance
aws rds create-db-instance \
    --db-instance-identifier document-analytics-db \
    --db-instance-class db.t3.micro \
    --engine mysql \
    --master-username admin \
    --master-user-password YourSecurePassword \
    --allocated-storage 20 \
    --db-name document_analytics
```

**Step 2: Launch EC2 Instance**
```bash
# Launch EC2 instance with Amazon Linux 2
aws ec2 run-instances \
    --image-id ami-0abcdef1234567890 \
    --count 1 \
    --instance-type t3.medium \
    --key-name your-key-pair \
    --security-group-ids sg-12345678
```

**Step 3: Configure EC2 Instance**
```bash
# SSH into instance
ssh -i your-key.pem ec2-user@your-instance-ip

# Install LAMP stack
sudo yum update -y
sudo yum install -y httpd php php-mysqlnd php-gd php-zip git

# Start Apache
sudo systemctl start httpd
sudo systemctl enable httpd

# Deploy application
cd /var/www/html
sudo git clone https://github.com/yourusername/document-analytics-system.git .
sudo chown -R apache:apache /var/www/html
sudo chmod -R 755 /var/www/html
```

### 2. Google Cloud Platform

#### Using Google Cloud Run
```bash
# Build and deploy container
gcloud builds submit --tag gcr.io/PROJECT-ID/document-analytics
gcloud run deploy --image gcr.io/PROJECT-ID/document-analytics --platform managed
```

#### Using Compute Engine
```bash
# Create VM instance
gcloud compute instances create document-analytics-vm \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --machine-type=e2-medium \
    --zone=us-central1-a
```

### 3. Microsoft Azure

#### Using Azure App Service
```bash
# Create resource group
az group create --name DocumentAnalyticsRG --location "East US"

# Create App Service plan
az appservice plan create --name DocumentAnalyticsPlan --resource-group DocumentAnalyticsRG --sku B1 --is-linux

# Create web app
az webapp create --resource-group DocumentAnalyticsRG --plan DocumentAnalyticsPlan --name document-analytics-app --runtime "PHP|8.1"
```

### 4. DigitalOcean

#### Using Droplets
```bash
# Create droplet using doctl
doctl compute droplet create document-analytics \
    --size s-2vcpu-2gb \
    --image ubuntu-20-04-x64 \
    --region nyc1 \
    --ssh-keys your-ssh-key-id
```

## 🐳 Docker Deployment

### Using Docker Compose (Recommended)

```bash
# Clone repository
git clone https://github.com/yourusername/document-analytics-system.git
cd document-analytics-system

# Start services
docker-compose -f deploy/docker-compose.yml up -d

# Access application
open http://localhost
```

### Manual Docker Setup

```bash
# Build image
docker build -t document-analytics -f deploy/Dockerfile .

# Run container
docker run -d \
    --name document-analytics-app \
    -p 80:80 \
    -v $(pwd)/uploads:/var/www/html/uploads \
    document-analytics
```

## 🌐 Shared Hosting Deployment

### cPanel/WHM Hosting

1. **Upload Files**
   - Upload all files to `public_html` directory
   - Ensure `uploads` directory has write permissions (755)

2. **Database Setup**
   - Create MySQL database through cPanel
   - Import `database/schema.sql`
   - Update `config/database.php` with credentials

3. **Configuration**
   - Verify PHP version is 7.4+
   - Enable required PHP extensions
   - Set file upload limits in PHP settings

### Plesk Hosting

1. **File Manager**
   - Upload files to `httpdocs` directory
   - Set proper file permissions

2. **Database**
   - Create database through Plesk panel
   - Import schema and configure connection

## 🔧 Environment Configuration

### Production Settings

**PHP Configuration (`php.ini`)**
```ini
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
memory_limit = 256M
display_errors = Off
log_errors = On
```

**Apache Configuration (`.htaccess`)**
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"
```

**Database Configuration**
```php
// config/database.php
private $host = 'your-db-host';
private $db_name = 'document_analytics';
private $username = 'your-username';
private $password = 'your-secure-password';
```

## 🔒 Security Configuration

### SSL/TLS Setup

**Let's Encrypt (Free SSL)**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-apache

# Obtain certificate
sudo certbot --apache -d yourdomain.com
```

**Cloudflare SSL**
- Enable SSL/TLS encryption in Cloudflare dashboard
- Set SSL mode to "Full (strict)"
- Enable "Always Use HTTPS"

### Security Headers

Add to `.htaccess` or server configuration:
```apache
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
Header always set Content-Security-Policy "default-src 'self'"
Header always set X-Frame-Options DENY
```

## 📊 Monitoring and Maintenance

### Health Checks

**Basic Health Check Endpoint**
```php
// health.php
<?php
$checks = [
    'database' => checkDatabase(),
    'uploads' => is_writable('uploads/'),
    'disk_space' => checkDiskSpace()
];

header('Content-Type: application/json');
echo json_encode($checks);
?>
```

### Log Monitoring

**Application Logs**
- Error logs: `/var/log/apache2/error.log`
- Access logs: `/var/log/apache2/access.log`
- Application logs: `logs/application.log`

### Backup Strategy

**Database Backup**
```bash
# Daily backup script
#!/bin/bash
mysqldump -u username -p password document_analytics > backup_$(date +%Y%m%d).sql
```

**File Backup**
```bash
# Backup uploads directory
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
```

## 🚀 Performance Optimization

### Caching

**Redis Setup**
```bash
# Install Redis
sudo apt install redis-server

# Configure Redis in application
// config/cache.php
$redis = new Redis();
$redis->connect('127.0.0.1', 6379);
```

**File Caching**
```php
// Enable OPcache
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
```

### CDN Integration

**Cloudflare Setup**
1. Add domain to Cloudflare
2. Update nameservers
3. Enable caching rules
4. Configure page rules for static assets

## 🔍 Troubleshooting

### Common Issues

**File Upload Issues**
```bash
# Check PHP limits
php -i | grep upload_max_filesize
php -i | grep post_max_size

# Check directory permissions
ls -la uploads/
```

**Database Connection Issues**
```bash
# Test MySQL connection
mysql -h hostname -u username -p

# Check PHP MySQL extension
php -m | grep mysql
```

**Performance Issues**
```bash
# Check server resources
top
df -h
free -m

# Analyze slow queries
mysql> SHOW PROCESSLIST;
```

### Log Analysis

**Apache Error Logs**
```bash
tail -f /var/log/apache2/error.log
```

**PHP Error Logs**
```bash
tail -f /var/log/php_errors.log
```

## 📞 Support

For deployment assistance:
- 📧 Email: <EMAIL>
- 📖 Documentation: https://docs.yourdomain.com
- 🐛 Issues: https://github.com/yourusername/document-analytics-system/issues

## 📋 Deployment Checklist

- [ ] Server requirements met
- [ ] Database created and configured
- [ ] Files uploaded and permissions set
- [ ] SSL certificate installed
- [ ] Security headers configured
- [ ] Backup strategy implemented
- [ ] Monitoring setup
- [ ] Performance optimization applied
- [ ] Health checks working
- [ ] Documentation updated

---

**Note**: Always test deployments in a staging environment before deploying to production.
