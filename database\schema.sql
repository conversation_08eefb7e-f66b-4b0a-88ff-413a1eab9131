-- Cloud-Based Document Analytics System
-- Database Schema
-- Created: 2025-06-22

-- Create database
CREATE DATABASE IF NOT EXISTS document_analytics CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE document_analytics;

-- Documents table - stores document metadata and content
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VA<PERSON>HAR(255) NOT NULL,
    title VARCHAR(500) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type ENUM('pdf', 'doc', 'docx') NOT NULL,
    content LONGTEXT,
    extracted_text LONGTEXT,
    classification VARCHAR(100) DEFAULT 'other',
    classification_confidence DECIMAL(5,2) DEFAULT 0.00,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('processing', 'completed', 'error') DEFAULT 'processing',
    INDEX idx_title (title),
    INDEX idx_classification (classification),
    INDEX idx_upload_date (upload_date),
    INDEX idx_file_type (file_type),
    FULLTEXT KEY ft_content (content, extracted_text, title)
) ENGINE=InnoDB;

-- Search history table - tracks search queries and performance
CREATE TABLE search_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_query VARCHAR(1000) NOT NULL,
    search_type ENUM('keyword', 'title', 'content') DEFAULT 'keyword',
    results_count INT DEFAULT 0,
    execution_time DECIMAL(8,4) DEFAULT 0.0000,
    search_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_ip VARCHAR(45),
    INDEX idx_search_date (search_date),
    INDEX idx_search_query (search_query(100))
) ENGINE=InnoDB;

-- Classification rules table - stores classification keywords and rules
CREATE TABLE classification_rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    keywords TEXT NOT NULL,
    weight DECIMAL(3,2) DEFAULT 1.00,
    rule_type ENUM('keyword', 'pattern', 'title') DEFAULT 'keyword',
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_active (is_active)
) ENGINE=InnoDB;

-- Performance metrics table - tracks system performance
CREATE TABLE performance_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    operation_type ENUM('upload', 'search', 'sort', 'classify') NOT NULL,
    execution_time DECIMAL(8,4) NOT NULL,
    records_processed INT DEFAULT 1,
    memory_usage BIGINT DEFAULT 0,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    additional_data JSON,
    INDEX idx_operation (operation_type),
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB;

-- Document keywords table - for better search performance
CREATE TABLE document_keywords (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    keyword VARCHAR(100) NOT NULL,
    frequency INT DEFAULT 1,
    position_data JSON,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_document_id (document_id),
    INDEX idx_keyword (keyword),
    UNIQUE KEY unique_doc_keyword (document_id, keyword)
) ENGINE=InnoDB;

-- System statistics table - stores overall system stats
CREATE TABLE system_statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stat_name VARCHAR(100) NOT NULL UNIQUE,
    stat_value BIGINT NOT NULL DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Insert initial classification rules
INSERT INTO classification_rules (category, keywords, weight, rule_type) VALUES
('academic', 'research,study,analysis,university,journal,paper,thesis,dissertation,academic,scholar', 1.0, 'keyword'),
('business', 'business,company,corporate,financial,revenue,profit,market,strategy,management,proposal', 1.0, 'keyword'),
('legal', 'legal,law,court,contract,agreement,terms,conditions,policy,regulation,compliance', 1.0, 'keyword'),
('technical', 'technical,technology,software,hardware,system,programming,development,engineering,manual', 1.0, 'keyword'),
('medical', 'medical,health,patient,doctor,treatment,diagnosis,medicine,clinical,hospital,therapy', 1.0, 'keyword');

-- Insert initial system statistics
INSERT INTO system_statistics (stat_name, stat_value) VALUES
('total_documents', 0),
('total_file_size', 0),
('total_searches', 0),
('total_classifications', 0);

-- Create indexes for better performance
CREATE INDEX idx_documents_fulltext ON documents(title, content(100));
CREATE INDEX idx_search_performance ON search_history(execution_time, results_count);
