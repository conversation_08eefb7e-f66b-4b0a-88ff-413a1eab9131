-- قاعدة بيانات بسيطة لمشروع إدارة الوثائق
-- Simple Document Management Database

CREATE DATABASE IF NOT EXISTS simple_docs CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE simple_docs;

-- جدول الوثائق
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(10) NOT NULL,
    content LONGTEXT,
    category VARCHAR(50) DEFAULT 'عام',
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_title (title),
    INDEX idx_category (category),
    INDEX idx_upload_date (upload_date),
    FULLTEXT KEY ft_content (title, content, original_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول البحثات (لحفظ إحصائيات البحث)
CREATE TABLE searches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_term VARCHAR(255) NOT NULL,
    results_count INT DEFAULT 0,
    execution_time DECIMAL(10,4) DEFAULT 0,
    search_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_search_term (search_term),
    INDEX idx_search_date (search_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات تجريبية
INSERT INTO documents (title, filename, original_name, file_size, file_type, content, category) VALUES
('مقدمة في علوم الحاسوب', 'doc1.pdf', 'intro_cs.pdf', 1024000, 'pdf', 'هذا كتاب يتحدث عن أساسيات علوم الحاسوب والبرمجة', 'أكاديمي'),
('تقرير مالي سنوي', 'doc2.pdf', 'annual_report.pdf', 2048000, 'pdf', 'التقرير المالي السنوي للشركة يظهر الأرباح والخسائر', 'تجاري'),
('دليل المستخدم', 'doc3.docx', 'user_manual.docx', 512000, 'docx', 'دليل استخدام النظام التقني الجديد', 'تقني');
