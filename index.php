<?php
/**
 * Cloud-Based Document Analytics System
 * Main Entry Point
 * 
 * <AUTHOR> Name
 * @version 1.0
 * @date 2025-06-22
 */

// Start session
session_start();

// Include configuration and classes
require_once 'config/database.php';
require_once 'classes/DocumentManager.php';
require_once 'classes/SearchEngine.php';
require_once 'classes/ClassificationEngine.php';
require_once 'classes/PerformanceTracker.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Check if database connection is successful
if (!$db) {
    die("Database connection failed. Please check your configuration.");
}

// Get the requested action
$action = isset($_GET['action']) ? $_GET['action'] : 'dashboard';

// Initialize performance tracker
$performanceTracker = new PerformanceTracker($db);
$startTime = microtime(true);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloud-Based Document Analytics System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-cloud"></i> Document Analytics
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $action == 'dashboard' ? 'active' : ''; ?>" href="index.php?action=dashboard">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $action == 'upload' ? 'active' : ''; ?>" href="index.php?action=upload">
                            <i class="fas fa-upload"></i> Upload Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $action == 'search' ? 'active' : ''; ?>" href="index.php?action=search">
                            <i class="fas fa-search"></i> Search
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $action == 'browse' ? 'active' : ''; ?>" href="index.php?action=browse">
                            <i class="fas fa-list"></i> Browse Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $action == 'classify' ? 'active' : ''; ?>" href="index.php?action=classify">
                            <i class="fas fa-tags"></i> Classification
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $action == 'statistics' ? 'active' : ''; ?>" href="index.php?action=statistics">
                            <i class="fas fa-chart-bar"></i> Statistics
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <?php
        // Route to appropriate page based on action
        switch ($action) {
            case 'dashboard':
                include 'pages/dashboard.php';
                break;
            case 'upload':
                include 'pages/upload.php';
                break;
            case 'search':
                include 'pages/search.php';
                break;
            case 'browse':
                include 'pages/browse.php';
                break;
            case 'classify':
                include 'pages/classify.php';
                break;
            case 'statistics':
                include 'pages/statistics.php';
                break;
            case 'view_document':
                include 'pages/view_document.php';
                break;
            default:
                include 'pages/dashboard.php';
                break;
        }
        ?>
    </div>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Cloud-Based Document Analytics System | Islamic University of Gaza</p>
            <small class="text-muted">Developed for Cloud and Distributed Systems Course (SICT 4313)</small>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>

    <?php
    // Track page load performance
    $endTime = microtime(true);
    $executionTime = $endTime - $startTime;
    
    if (Config::ENABLE_PERFORMANCE_TRACKING) {
        $performanceTracker->trackOperation('page_load', $executionTime, 1, [
            'action' => $action,
            'memory_usage' => memory_get_peak_usage(true)
        ]);
    }
    ?>
</body>
</html>
