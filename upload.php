<?php
/**
 * صفحة رفع الملفات
 */

$message = '';
$message_type = '';

// معالجة رفع الملف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['document'])) {
    $file = $_FILES['document'];
    
    // التحقق من وجود أخطاء في الرفع
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $message = 'خطأ في رفع الملف';
        $message_type = 'danger';
    }
    // التحقق من حجم الملف
    elseif ($file['size'] > MAX_FILE_SIZE) {
        $message = 'حجم الملف كبير جداً. الحد الأقصى ' . formatFileSize(MAX_FILE_SIZE);
        $message_type = 'danger';
    }
    // التحقق من نوع الملف
    else {
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_extension, ALLOWED_TYPES)) {
            $message = 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', ALLOWED_TYPES);
            $message_type = 'danger';
        }
        else {
            // إنشاء اسم ملف فريد
            $filename = uniqid() . '.' . $file_extension;
            $filepath = UPLOAD_DIR . $filename;
            
            // نقل الملف إلى مجلد الرفع
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                // استخراج النص من الملف
                $content = '';
                $extraction_success = false;

                if ($file_extension == 'pdf') {
                    $content = extractTextFromPDF($filepath);
                    $extraction_success = !empty(trim($content)) && !str_contains($content, 'لم يتم استخراج النص');
                } elseif (in_array($file_extension, ['doc', 'docx'])) {
                    $content = extractTextFromWord($filepath);
                    $extraction_success = !empty(trim($content)) && !str_contains($content, 'لم يتم استخراج النص');
                }

                // استخراج العنوان من المحتوى (وليس من اسم الملف)
                $title = pathinfo($file['name'], PATHINFO_FILENAME); // العنوان الافتراضي

                if ($extraction_success && !empty($content)) {
                    // محاولة استخراج العنوان من السطر الأول
                    $lines = explode("\n", trim($content));
                    $firstLine = trim($lines[0] ?? '');

                    // إذا كان السطر الأول مناسب كعنوان
                    if (!empty($firstLine) && strlen($firstLine) >= 10 && strlen($firstLine) <= 200) {
                        // تنظيف العنوان
                        $firstLine = preg_replace('/[^\p{L}\p{N}\s\-_.,()]/u', '', $firstLine);
                        if (!empty(trim($firstLine))) {
                            $title = trim($firstLine);
                        }
                    }

                    // إذا لم نجد عنوان مناسب، ابحث في أول 500 حرف
                    if ($title === pathinfo($file['name'], PATHINFO_FILENAME)) {
                        $excerpt = substr($content, 0, 500);
                        $sentences = preg_split('/[.!?]+/', $excerpt);
                        foreach ($sentences as $sentence) {
                            $sentence = trim($sentence);
                            if (strlen($sentence) >= 15 && strlen($sentence) <= 150) {
                                $title = $sentence;
                                break;
                            }
                        }
                    }
                }
                
                // تصنيف الوثيقة
                $category = classifyDocument($title, $content);
                
                // حفظ في قاعدة البيانات
                $stmt = $pdo->prepare("INSERT INTO documents (title, filename, original_name, file_size, file_type, content, category) VALUES (?, ?, ?, ?, ?, ?, ?)");
                
                if ($stmt->execute([$title, $filename, $file['name'], $file['size'], $file_extension, $content, $category])) {
                    $extraction_msg = $extraction_success ? 'تم استخراج النص بنجاح' : 'لم يتم استخراج النص (يمكن البحث باسم الملف)';
                    $message = "تم رفع الملف بنجاح!<br>";
                    $message .= "العنوان المستخرج: " . htmlspecialchars($title) . "<br>";
                    $message .= "التصنيف: " . $category . "<br>";
                    $message .= "استخراج النص: " . $extraction_msg;
                    $message_type = 'success';
                } else {
                    $message = 'خطأ في حفظ الملف في قاعدة البيانات';
                    $message_type = 'danger';
                    unlink($filepath); // حذف الملف إذا فشل الحفظ
                }
            } else {
                $message = 'خطأ في نقل الملف';
                $message_type = 'danger';
            }
        }
    }
}
?>

<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-upload"></i> رفع وثيقة جديدة</h2>
        <p class="text-muted">يمكنك رفع ملفات PDF أو Word وسيتم تصنيفها تلقائياً</p>
    </div>
</div>

<?php if ($message): ?>
<div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cloud-upload-alt"></i> رفع ملف</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="document" class="form-label">اختر الملف</label>
                        <input type="file" class="form-control" id="document" name="document" 
                               accept=".pdf,.doc,.docx" required>
                        <div class="form-text">
                            الأنواع المدعومة: PDF, DOC, DOCX | الحد الأقصى: <?php echo formatFileSize(MAX_FILE_SIZE); ?>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> رفع الملف
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> معلومات مهمة</h5>
            </div>
            <div class="card-body">
                <h6>الأنواع المدعومة:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-pdf text-danger"></i> ملفات PDF</li>
                    <li><i class="fas fa-file-word text-primary"></i> ملفات Word (.doc, .docx)</li>
                </ul>
                
                <h6 class="mt-3">ما يحدث عند الرفع:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> استخراج النص من الملف</li>
                    <li><i class="fas fa-check text-success"></i> تحديد العنوان تلقائياً</li>
                    <li><i class="fas fa-check text-success"></i> تصنيف الوثيقة</li>
                    <li><i class="fas fa-check text-success"></i> حفظ في قاعدة البيانات</li>
                </ul>
                
                <h6 class="mt-3">فئات التصنيف:</h6>
                <div class="mb-2">
                    <span class="category-badge category-أكاديمي">أكاديمي</span>
                    <span class="category-badge category-تجاري">تجاري</span>
                </div>
                <div class="mb-2">
                    <span class="category-badge category-قانوني">قانوني</span>
                    <span class="category-badge category-تقني">تقني</span>
                </div>
                <div class="mb-2">
                    <span class="category-badge category-طبي">طبي</span>
                    <span class="category-badge category-عام">عام</span>
                </div>
            </div>
        </div>
        
        <!-- آخر الملفات المرفوعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> آخر الملفات</h5>
            </div>
            <div class="card-body">
                <?php
                $stmt = $pdo->query("SELECT * FROM documents ORDER BY upload_date DESC LIMIT 3");
                $recent = $stmt->fetchAll();
                
                if (empty($recent)): ?>
                    <p class="text-muted text-center">لا توجد ملفات مرفوعة</p>
                <?php else: ?>
                    <?php foreach ($recent as $doc): ?>
                        <div class="mb-2 pb-2 border-bottom">
                            <div class="fw-bold">
                                <a href="index.php?page=view&id=<?php echo $doc['id']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars(substr($doc['title'], 0, 30)); ?>
                                    <?php if (strlen($doc['title']) > 30) echo '...'; ?>
                                </a>
                            </div>
                            <small class="text-muted">
                                <span class="category-badge category-<?php echo $doc['category']; ?>">
                                    <?php echo $doc['category']; ?>
                                </span>
                                <?php echo formatFileSize($doc['file_size']); ?>
                            </small>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة الملف المختار
document.getElementById('document').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileSize = file.size;
        const maxSize = <?php echo MAX_FILE_SIZE; ?>;
        
        if (fileSize > maxSize) {
            alert('حجم الملف كبير جداً. الحد الأقصى <?php echo formatFileSize(MAX_FILE_SIZE); ?>');
            this.value = '';
            return;
        }
        
        const allowedTypes = <?php echo json_encode(ALLOWED_TYPES); ?>;
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            alert('نوع الملف غير مدعوم. الأنواع المدعومة: ' + allowedTypes.join(', '));
            this.value = '';
            return;
        }
        
        console.log('ملف صالح: ' + file.name + ' (' + (fileSize / 1024 / 1024).toFixed(2) + ' MB)');
    }
});
</script>
