<?php
/**
 * Browse Documents Page
 * Display and sort documents with pagination
 */

// Get sorting parameters
$sortBy = $_GET['sort'] ?? 'upload_date';
$sortOrder = $_GET['order'] ?? 'DESC';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 10;

// Get filter parameters
$filterType = $_GET['filter_type'] ?? '';
$filterClassification = $_GET['filter_classification'] ?? '';

// Initialize document manager
$documentManager = new DocumentManager($db);

// Build filter array
$filters = [];
if ($filterType) $filters['file_type'] = $filterType;
if ($filterClassification) $filters['classification'] = $filterClassification;

// Get documents with sorting and filtering
try {
    $sql = "SELECT d.*, COUNT(*) OVER() as total_count 
            FROM documents d 
            WHERE d.status = 'completed'";
    
    $params = [];
    
    // Add filters
    if ($filterType) {
        $sql .= " AND d.file_type = :filter_type";
        $params[':filter_type'] = $filterType;
    }
    
    if ($filterClassification) {
        $sql .= " AND d.classification = :filter_classification";
        $params[':filter_classification'] = $filterClassification;
    }
    
    // Add sorting
    $allowedSortFields = ['title', 'upload_date', 'file_size', 'classification', 'file_type'];
    $sortBy = in_array($sortBy, $allowedSortFields) ? $sortBy : 'upload_date';
    $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
    
    $sql .= " ORDER BY d.$sortBy $sortOrder";
    
    // Add pagination
    $offset = ($page - 1) * $limit;
    $sql .= " LIMIT :limit OFFSET :offset";
    $params[':limit'] = $limit;
    $params[':offset'] = $offset;
    
    $stmt = $db->prepare($sql);
    foreach ($params as $key => $value) {
        if ($key === ':limit' || $key === ':offset') {
            $stmt->bindValue($key, $value, PDO::PARAM_INT);
        } else {
            $stmt->bindValue($key, $value);
        }
    }
    $stmt->execute();
    
    $documents = $stmt->fetchAll();
    $totalCount = $documents ? $documents[0]['total_count'] : 0;
    $totalPages = ceil($totalCount / $limit);
    
} catch (Exception $e) {
    $documents = [];
    $totalCount = 0;
    $totalPages = 0;
    $error = $e->getMessage();
}

// Get available file types and classifications for filters
try {
    $sql = "SELECT DISTINCT file_type FROM documents WHERE status = 'completed' ORDER BY file_type";
    $stmt = $db->query($sql);
    $fileTypes = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $sql = "SELECT DISTINCT classification FROM documents WHERE status = 'completed' ORDER BY classification";
    $stmt = $db->query($sql);
    $classifications = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $fileTypes = [];
    $classifications = [];
}

function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

function getSortIcon($field, $currentSort, $currentOrder) {
    if ($field === $currentSort) {
        return $currentOrder === 'ASC' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    }
    return 'fas fa-sort';
}

function getSortUrl($field, $currentSort, $currentOrder) {
    $newOrder = ($field === $currentSort && $currentOrder === 'ASC') ? 'DESC' : 'ASC';
    $params = $_GET;
    $params['sort'] = $field;
    $params['order'] = $newOrder;
    unset($params['page']); // Reset to first page when sorting
    return 'index.php?action=browse&' . http_build_query($params);
}
?>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-list"></i> Browse Documents
        </h1>
    </div>
</div>

<!-- Filters and Search -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <input type="hidden" name="action" value="browse">
                    
                    <div class="col-md-3">
                        <label for="filter_type" class="form-label">File Type</label>
                        <select name="filter_type" id="filter_type" class="form-select">
                            <option value="">All Types</option>
                            <?php foreach ($fileTypes as $type): ?>
                            <option value="<?php echo $type; ?>" <?php echo $filterType === $type ? 'selected' : ''; ?>>
                                <?php echo strtoupper($type); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="filter_classification" class="form-label">Classification</label>
                        <select name="filter_classification" id="filter_classification" class="form-select">
                            <option value="">All Classifications</option>
                            <?php foreach ($classifications as $classification): ?>
                            <option value="<?php echo $classification; ?>" <?php echo $filterClassification === $classification ? 'selected' : ''; ?>>
                                <?php echo ucfirst($classification); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="sort" class="form-label">Sort By</label>
                        <select name="sort" id="sort" class="form-select">
                            <option value="upload_date" <?php echo $sortBy === 'upload_date' ? 'selected' : ''; ?>>Upload Date</option>
                            <option value="title" <?php echo $sortBy === 'title' ? 'selected' : ''; ?>>Title</option>
                            <option value="file_size" <?php echo $sortBy === 'file_size' ? 'selected' : ''; ?>>File Size</option>
                            <option value="classification" <?php echo $sortBy === 'classification' ? 'selected' : ''; ?>>Classification</option>
                            <option value="file_type" <?php echo $sortBy === 'file_type' ? 'selected' : ''; ?>>File Type</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="order" class="form-label">Order</label>
                        <select name="order" id="order" class="form-select">
                            <option value="DESC" <?php echo $sortOrder === 'DESC' ? 'selected' : ''; ?>>Descending</option>
                            <option value="ASC" <?php echo $sortOrder === 'ASC' ? 'selected' : ''; ?>>Ascending</option>
                        </select>
                    </div>
                    
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Apply Filters
                        </button>
                        <a href="index.php?action=browse" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Results Summary -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <?php echo number_format($totalCount); ?> Document(s) Found
                </h5>
                <?php if ($filterType || $filterClassification): ?>
                <small class="text-muted">
                    Filtered by: 
                    <?php if ($filterType): ?>
                        Type: <?php echo strtoupper($filterType); ?>
                    <?php endif; ?>
                    <?php if ($filterType && $filterClassification): ?> | <?php endif; ?>
                    <?php if ($filterClassification): ?>
                        Classification: <?php echo ucfirst($filterClassification); ?>
                    <?php endif; ?>
                </small>
                <?php endif; ?>
            </div>
            <div>
                <small class="text-muted">
                    Page <?php echo $page; ?> of <?php echo $totalPages; ?>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Documents Table -->
<?php if (empty($documents)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                <h4>No Documents Found</h4>
                <p class="text-muted">
                    <?php if ($filterType || $filterClassification): ?>
                        Try adjusting your filters or <a href="index.php?action=browse">clear all filters</a>.
                    <?php else: ?>
                        Start by <a href="index.php?action=upload">uploading some documents</a>.
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <a href="<?php echo getSortUrl('title', $sortBy, $sortOrder); ?>" class="text-decoration-none">
                                        Title <i class="<?php echo getSortIcon('title', $sortBy, $sortOrder); ?>"></i>
                                    </a>
                                </th>
                                <th>
                                    <a href="<?php echo getSortUrl('file_type', $sortBy, $sortOrder); ?>" class="text-decoration-none">
                                        Type <i class="<?php echo getSortIcon('file_type', $sortBy, $sortOrder); ?>"></i>
                                    </a>
                                </th>
                                <th>
                                    <a href="<?php echo getSortUrl('classification', $sortBy, $sortOrder); ?>" class="text-decoration-none">
                                        Classification <i class="<?php echo getSortIcon('classification', $sortBy, $sortOrder); ?>"></i>
                                    </a>
                                </th>
                                <th>
                                    <a href="<?php echo getSortUrl('file_size', $sortBy, $sortOrder); ?>" class="text-decoration-none">
                                        Size <i class="<?php echo getSortIcon('file_size', $sortBy, $sortOrder); ?>"></i>
                                    </a>
                                </th>
                                <th>
                                    <a href="<?php echo getSortUrl('upload_date', $sortBy, $sortOrder); ?>" class="text-decoration-none">
                                        Upload Date <i class="<?php echo getSortIcon('upload_date', $sortBy, $sortOrder); ?>"></i>
                                    </a>
                                </th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($documents as $doc): ?>
                            <tr>
                                <td>
                                    <a href="index.php?action=view_document&id=<?php echo $doc['id']; ?>" 
                                       class="document-title">
                                        <?php echo htmlspecialchars($doc['title']); ?>
                                    </a>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($doc['original_filename']); ?>
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <?php echo strtoupper($doc['file_type']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge classification-<?php echo $doc['classification']; ?>">
                                        <?php echo ucfirst($doc['classification']); ?>
                                    </span>
                                    <?php if ($doc['classification_confidence'] > 0): ?>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo number_format($doc['classification_confidence'], 1); ?>% confidence
                                    </small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo formatFileSize($doc['file_size']); ?></td>
                                <td>
                                    <?php echo date('M j, Y', strtotime($doc['upload_date'])); ?>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo date('g:i A', strtotime($doc['upload_date'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="index.php?action=view_document&id=<?php echo $doc['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary" title="View Document">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo htmlspecialchars($doc['file_path']); ?>" 
                                           class="btn btn-sm btn-outline-success" 
                                           title="Download" 
                                           download="<?php echo htmlspecialchars($doc['original_filename']); ?>">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-danger" 
                                                title="Delete"
                                                onclick="deleteDocument(<?php echo $doc['id']; ?>, '<?php echo htmlspecialchars($doc['title']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
<?php if ($totalPages > 1): ?>
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Document pagination">
            <ul class="pagination justify-content-center">
                <!-- Previous Page -->
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo 'index.php?action=browse&' . http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                </li>
                <?php endif; ?>
                
                <!-- Page Numbers -->
                <?php
                $startPage = max(1, $page - 2);
                $endPage = min($totalPages, $page + 2);
                
                if ($startPage > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo 'index.php?action=browse&' . http_build_query(array_merge($_GET, ['page' => 1])); ?>">1</a>
                </li>
                <?php if ($startPage > 2): ?>
                <li class="page-item disabled"><span class="page-link">...</span></li>
                <?php endif; ?>
                <?php endif; ?>
                
                <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                    <a class="page-link" href="<?php echo 'index.php?action=browse&' . http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($endPage < $totalPages): ?>
                <?php if ($endPage < $totalPages - 1): ?>
                <li class="page-item disabled"><span class="page-link">...</span></li>
                <?php endif; ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo 'index.php?action=browse&' . http_build_query(array_merge($_GET, ['page' => $totalPages])); ?>">
                        <?php echo $totalPages; ?>
                    </a>
                </li>
                <?php endif; ?>
                
                <!-- Next Page -->
                <?php if ($page < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo 'index.php?action=browse&' . http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
</div>
<?php endif; ?>
<?php endif; ?>

<script>
function deleteDocument(id, title) {
    if (confirm('Are you sure you want to delete "' + title + '"? This action cannot be undone.')) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'api/delete_document.php';
        
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'document_id';
        idInput.value = id;
        
        form.appendChild(idInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
