<?php
/**
 * صفحة عرض جميع الوثائق
 */

// معاملات الترتيب والتصفية
$sort_by = $_GET['sort'] ?? 'upload_date';
$order = $_GET['order'] ?? 'DESC';
$category_filter = $_GET['category'] ?? '';

// التحقق من صحة معاملات الترتيب
$allowed_sorts = ['title', 'upload_date', 'file_size', 'category'];
if (!in_array($sort_by, $allowed_sorts)) {
    $sort_by = 'upload_date';
}

$order = strtoupper($order) === 'ASC' ? 'ASC' : 'DESC';

// بناء الاستعلام
$sql = "SELECT * FROM documents";
$params = [];

if (!empty($category_filter)) {
    $sql .= " WHERE category = :category";
    $params[':category'] = $category_filter;
}

$sql .= " ORDER BY $sort_by $order";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$documents = $stmt->fetchAll();

// جلب الفئات المتاحة للتصفية
$stmt = $pdo->query("SELECT DISTINCT category FROM documents ORDER BY category");
$categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

// دالة لإنشاء رابط الترتيب
function getSortLink($field, $current_sort, $current_order) {
    $new_order = ($field === $current_sort && $current_order === 'ASC') ? 'DESC' : 'ASC';
    $params = $_GET;
    $params['sort'] = $field;
    $params['order'] = $new_order;
    return 'index.php?' . http_build_query($params);
}

// دالة لإظهار أيقونة الترتيب
function getSortIcon($field, $current_sort, $current_order) {
    if ($field === $current_sort) {
        return $current_order === 'ASC' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    }
    return 'fas fa-sort';
}
?>

<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-list"></i> جميع الوثائق</h2>
        <p class="text-muted">عرض وترتيب جميع الوثائق المرفوعة</p>
    </div>
</div>

<!-- أدوات التصفية والترتيب -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <input type="hidden" name="page" value="documents">
                    
                    <div class="col-md-3">
                        <label for="category" class="form-label">تصفية حسب الفئة</label>
                        <select name="category" id="category" class="form-select">
                            <option value="">جميع الفئات</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat; ?>" <?php echo $category_filter === $cat ? 'selected' : ''; ?>>
                                    <?php echo $cat; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="sort" class="form-label">ترتيب حسب</label>
                        <select name="sort" id="sort" class="form-select">
                            <option value="upload_date" <?php echo $sort_by === 'upload_date' ? 'selected' : ''; ?>>تاريخ الرفع</option>
                            <option value="title" <?php echo $sort_by === 'title' ? 'selected' : ''; ?>>العنوان</option>
                            <option value="file_size" <?php echo $sort_by === 'file_size' ? 'selected' : ''; ?>>حجم الملف</option>
                            <option value="category" <?php echo $sort_by === 'category' ? 'selected' : ''; ?>>الفئة</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="order" class="form-label">الترتيب</label>
                        <select name="order" id="order" class="form-select">
                            <option value="DESC" <?php echo $order === 'DESC' ? 'selected' : ''; ?>>تنازلي</option>
                            <option value="ASC" <?php echo $order === 'ASC' ? 'selected' : ''; ?>>تصاعدي</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> تطبيق
                            </button>
                            <a href="index.php?page=documents" class="btn btn-secondary">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- عرض النتائج -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>
                    <i class="fas fa-file-alt"></i> 
                    الوثائق (<?php echo count($documents); ?>)
                    <?php if ($category_filter): ?>
                        - فئة: <?php echo $category_filter; ?>
                    <?php endif; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($documents)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5>لا توجد وثائق</h5>
                        <?php if ($category_filter): ?>
                            <p class="text-muted">لا توجد وثائق في فئة "<?php echo $category_filter; ?>"</p>
                            <a href="index.php?page=documents" class="btn btn-primary">عرض جميع الوثائق</a>
                        <?php else: ?>
                            <p class="text-muted">ابدأ برفع بعض الوثائق</p>
                            <a href="index.php?page=upload" class="btn btn-primary">رفع وثيقة</a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <!-- عرض جدولي للشاشات الكبيرة -->
                    <div class="table-responsive d-none d-md-block">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        <a href="<?php echo getSortLink('title', $sort_by, $order); ?>" class="text-decoration-none">
                                            العنوان <i class="<?php echo getSortIcon('title', $sort_by, $order); ?>"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a href="<?php echo getSortLink('category', $sort_by, $order); ?>" class="text-decoration-none">
                                            الفئة <i class="<?php echo getSortIcon('category', $sort_by, $order); ?>"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a href="<?php echo getSortLink('file_size', $sort_by, $order); ?>" class="text-decoration-none">
                                            الحجم <i class="<?php echo getSortIcon('file_size', $sort_by, $order); ?>"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a href="<?php echo getSortLink('upload_date', $sort_by, $order); ?>" class="text-decoration-none">
                                            تاريخ الرفع <i class="<?php echo getSortIcon('upload_date', $sort_by, $order); ?>"></i>
                                        </a>
                                    </th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($documents as $doc): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold">
                                                <a href="index.php?page=view&id=<?php echo $doc['id']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($doc['title']); ?>
                                                </a>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($doc['original_name']); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="category-badge category-<?php echo $doc['category']; ?>">
                                                <?php echo $doc['category']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo formatFileSize($doc['file_size']); ?></td>
                                        <td>
                                            <?php echo date('Y-m-d', strtotime($doc['upload_date'])); ?>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo date('H:i', strtotime($doc['upload_date'])); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <a href="index.php?page=view&id=<?php echo $doc['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- عرض البطاقات للشاشات الصغيرة -->
                    <div class="d-md-none">
                        <?php foreach ($documents as $doc): ?>
                            <div class="document-item">
                                <div class="row">
                                    <div class="col-12">
                                        <h6 class="mb-1">
                                            <a href="index.php?page=view&id=<?php echo $doc['id']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($doc['title']); ?>
                                            </a>
                                        </h6>
                                        <div class="mb-2">
                                            <span class="category-badge category-<?php echo $doc['category']; ?>">
                                                <?php echo $doc['category']; ?>
                                            </span>
                                            <span class="badge bg-secondary ms-2">
                                                <?php echo strtoupper($doc['file_type']); ?>
                                            </span>
                                        </div>
                                        <small class="text-muted">
                                            <i class="fas fa-file"></i> <?php echo htmlspecialchars($doc['original_name']); ?>
                                            | <i class="fas fa-hdd"></i> <?php echo formatFileSize($doc['file_size']); ?>
                                            | <i class="fas fa-calendar"></i> <?php echo date('Y-m-d H:i', strtotime($doc['upload_date'])); ?>
                                        </small>
                                        <div class="mt-2">
                                            <a href="index.php?page=view&id=<?php echo $doc['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
