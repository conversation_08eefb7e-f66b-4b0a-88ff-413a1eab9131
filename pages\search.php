<?php
/**
 * Search Page
 * Advanced document search with highlighting and filters
 */

$searchResults = [];
$searchQuery = '';
$executionTime = 0;
$totalResults = 0;
$currentPage = 1;
$totalPages = 0;

// Handle search request
if ($_SERVER['REQUEST_METHOD'] === 'GET' && !empty($_GET['q'])) {
    $searchQuery = trim($_GET['q']);
    $currentPage = max(1, (int)($_GET['page'] ?? 1));
    
    // Get filters
    $filters = [];
    if (!empty($_GET['file_type'])) {
        $filters['file_type'] = $_GET['file_type'];
    }
    if (!empty($_GET['classification'])) {
        $filters['classification'] = $_GET['classification'];
    }
    if (!empty($_GET['date_from'])) {
        $filters['date_from'] = $_GET['date_from'];
    }
    if (!empty($_GET['date_to'])) {
        $filters['date_to'] = $_GET['date_to'];
    }
    
    // Perform search
    $searchEngine = new SearchEngine($db);
    $searchResult = $searchEngine->searchDocuments($searchQuery, $filters, $currentPage, 10);
    
    $searchResults = $searchResult['results'];
    $totalResults = $searchResult['total_count'];
    $totalPages = $searchResult['total_pages'];
    $executionTime = $searchResult['execution_time'];
}

// Get available options for filters
try {
    $sql = "SELECT DISTINCT file_type FROM documents WHERE status = 'completed' ORDER BY file_type";
    $stmt = $db->query($sql);
    $fileTypes = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $sql = "SELECT DISTINCT classification FROM documents WHERE status = 'completed' ORDER BY classification";
    $stmt = $db->query($sql);
    $classifications = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $fileTypes = [];
    $classifications = [];
}

// Get search suggestions
$searchEngine = new SearchEngine($db);
$popularSearches = $searchEngine->getPopularSearchTerms(5);

function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-search"></i> Search Documents
        </h1>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" id="searchForm">
                    <input type="hidden" name="action" value="search">
                    
                    <!-- Main Search -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="input-group input-group-lg">
                                <input type="text" 
                                       name="q" 
                                       id="searchQuery"
                                       class="form-control" 
                                       placeholder="Enter keywords to search documents..."
                                       value="<?php echo htmlspecialchars($searchQuery); ?>"
                                       autocomplete="off">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                            <div id="searchSuggestions" class="mt-2" style="display: none;"></div>
                        </div>
                    </div>
                    
                    <!-- Advanced Filters -->
                    <div class="row">
                        <div class="col-md-3">
                            <label for="file_type" class="form-label">File Type</label>
                            <select name="file_type" id="file_type" class="form-select">
                                <option value="">All Types</option>
                                <?php foreach ($fileTypes as $type): ?>
                                <option value="<?php echo $type; ?>" <?php echo ($_GET['file_type'] ?? '') === $type ? 'selected' : ''; ?>>
                                    <?php echo strtoupper($type); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="classification" class="form-label">Classification</label>
                            <select name="classification" id="classification" class="form-select">
                                <option value="">All Classifications</option>
                                <?php foreach ($classifications as $classification): ?>
                                <option value="<?php echo $classification; ?>" <?php echo ($_GET['classification'] ?? '') === $classification ? 'selected' : ''; ?>>
                                    <?php echo ucfirst($classification); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" 
                                   name="date_from" 
                                   id="date_from" 
                                   class="form-control"
                                   value="<?php echo $_GET['date_from'] ?? ''; ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" 
                                   name="date_to" 
                                   id="date_to" 
                                   class="form-control"
                                   value="<?php echo $_GET['date_to'] ?? ''; ?>">
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Search with Filters
                            </button>
                            <a href="index.php?action=search" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Clear All
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Popular Searches -->
<?php if (empty($searchQuery) && !empty($popularSearches)): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-fire"></i> Popular Searches
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($popularSearches as $search): ?>
                <a href="index.php?action=search&q=<?php echo urlencode($search['search_query']); ?>" 
                   class="btn btn-outline-primary btn-sm me-2 mb-2">
                    <?php echo htmlspecialchars($search['search_query']); ?>
                    <span class="badge bg-secondary"><?php echo $search['frequency']; ?></span>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Search Results -->
<?php if ($searchQuery): ?>
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    Search Results for "<?php echo htmlspecialchars($searchQuery); ?>"
                </h5>
                <small class="text-muted">
                    Found <?php echo number_format($totalResults); ?> result(s) in <?php echo number_format($executionTime, 3); ?> seconds
                </small>
            </div>
            <?php if ($totalPages > 1): ?>
            <div>
                <small class="text-muted">
                    Page <?php echo $currentPage; ?> of <?php echo $totalPages; ?>
                </small>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if (empty($searchResults)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-search fa-4x text-muted mb-3"></i>
                <h4>No Results Found</h4>
                <p class="text-muted">
                    Try different keywords or adjust your search filters.
                </p>
                <div class="mt-3">
                    <h6>Search Tips:</h6>
                    <ul class="list-unstyled text-muted">
                        <li>• Use different or more general keywords</li>
                        <li>• Check your spelling</li>
                        <li>• Remove filters to broaden your search</li>
                        <li>• Try searching for partial words</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
<div class="search-results">
    <?php foreach ($searchResults as $result): ?>
    <div class="search-result-item">
        <div class="row">
            <div class="col-md-8">
                <h5 class="search-result-title">
                    <a href="index.php?action=view_document&id=<?php echo $result['id']; ?>&highlight=<?php echo urlencode($searchQuery); ?>">
                        <?php echo $result['highlighted_title']; ?>
                    </a>
                </h5>
                
                <div class="search-result-snippet">
                    <?php echo $result['snippet']; ?>
                </div>
                
                <div class="document-meta">
                    <span class="badge bg-secondary me-2">
                        <?php echo strtoupper($result['file_type']); ?>
                    </span>
                    <span class="badge classification-<?php echo $result['classification']; ?> me-2">
                        <?php echo ucfirst($result['classification']); ?>
                    </span>
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i> <?php echo date('M j, Y', strtotime($result['upload_date'])); ?>
                        <i class="fas fa-hdd ms-2"></i> <?php echo formatFileSize($result['file_size']); ?>
                        <?php if ($result['relevance_score'] > 0): ?>
                        <i class="fas fa-star ms-2"></i> Relevance: <?php echo number_format($result['relevance_score'], 2); ?>
                        <?php endif; ?>
                    </small>
                </div>
            </div>
            
            <div class="col-md-4 text-end">
                <div class="btn-group" role="group">
                    <a href="index.php?action=view_document&id=<?php echo $result['id']; ?>&highlight=<?php echo urlencode($searchQuery); ?>" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i> View
                    </a>
                    <a href="<?php echo htmlspecialchars($result['filename']); ?>" 
                       class="btn btn-outline-success" 
                       download="<?php echo htmlspecialchars($result['original_filename']); ?>">
                        <i class="fas fa-download"></i> Download
                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- Pagination -->
<?php if ($totalPages > 1): ?>
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Search results pagination">
            <ul class="pagination justify-content-center">
                <!-- Previous Page -->
                <?php if ($currentPage > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo 'index.php?action=search&' . http_build_query(array_merge($_GET, ['page' => $currentPage - 1])); ?>">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                </li>
                <?php endif; ?>
                
                <!-- Page Numbers -->
                <?php
                $startPage = max(1, $currentPage - 2);
                $endPage = min($totalPages, $currentPage + 2);
                
                for ($i = $startPage; $i <= $endPage; $i++): ?>
                <li class="page-item <?php echo $i === $currentPage ? 'active' : ''; ?>">
                    <a class="page-link" href="<?php echo 'index.php?action=search&' . http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <!-- Next Page -->
                <?php if ($currentPage < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo 'index.php?action=search&' . http_build_query(array_merge($_GET, ['page' => $currentPage + 1])); ?>">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
</div>
<?php endif; ?>
<?php endif; ?>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchQuery');
    const suggestionsDiv = document.getElementById('searchSuggestions');
    let searchTimeout;
    
    // Search suggestions
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            suggestionsDiv.style.display = 'none';
            return;
        }
        
        searchTimeout = setTimeout(() => {
            fetch(`api/search_suggestions.php?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(suggestions => {
                    if (suggestions.length > 0) {
                        suggestionsDiv.innerHTML = suggestions.map(suggestion => 
                            `<button type="button" class="btn btn-sm btn-outline-secondary me-2 mb-1" onclick="selectSuggestion('${suggestion}')">${suggestion}</button>`
                        ).join('');
                        suggestionsDiv.style.display = 'block';
                    } else {
                        suggestionsDiv.style.display = 'none';
                    }
                })
                .catch(() => {
                    suggestionsDiv.style.display = 'none';
                });
        }, 300);
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !suggestionsDiv.contains(e.target)) {
            suggestionsDiv.style.display = 'none';
        }
    });
});

function selectSuggestion(suggestion) {
    document.getElementById('searchQuery').value = suggestion;
    document.getElementById('searchSuggestions').style.display = 'none';
    document.getElementById('searchForm').submit();
}

// Auto-focus search input
document.getElementById('searchQuery').focus();
</script>
