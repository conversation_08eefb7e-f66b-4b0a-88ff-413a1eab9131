<?php
/**
 * Delete Document API
 * Handles document deletion requests
 */

session_start();

// Include required files
require_once '../config/database.php';
require_once '../classes/DocumentManager.php';

try {
    // Check if request is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        header('Location: ../index.php?action=browse&error=invalid_request');
        exit;
    }
    
    // Get document ID
    $documentId = (int)($_POST['document_id'] ?? 0);
    
    if ($documentId <= 0) {
        header('Location: ../index.php?action=browse&error=invalid_document');
        exit;
    }
    
    // Initialize database and document manager
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        header('Location: ../index.php?action=browse&error=database_error');
        exit;
    }
    
    $documentManager = new DocumentManager($db);
    
    // Delete document
    $result = $documentManager->deleteDocument($documentId);
    
    if ($result) {
        header('Location: ../index.php?action=browse&success=document_deleted');
    } else {
        header('Location: ../index.php?action=browse&error=delete_failed');
    }
    
} catch (Exception $e) {
    error_log("Delete document error: " . $e->getMessage());
    header('Location: ../index.php?action=browse&error=system_error');
}
?>
