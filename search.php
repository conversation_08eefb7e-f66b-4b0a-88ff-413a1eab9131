<?php
/**
 * صفحة البحث
 */

$search_query = $_GET['q'] ?? '';
$search_results = [];
$search_count = 0;

// معالجة البحث
if (!empty($search_query)) {
    $start_time = microtime(true);
    $search_term = trim($search_query);

    // البحث المحسن باستخدام FULLTEXT
    try {
        // أولاً: البحث باستخدام FULLTEXT للحصول على نتائج أفضل
        $sql = "SELECT *, MATCH(title, content, original_name) AGAINST(:query IN NATURAL LANGUAGE MODE) as relevance_score
                FROM documents
                WHERE MATCH(title, content, original_name) AGAINST(:query IN NATURAL LANGUAGE MODE)
                ORDER BY relevance_score DESC, upload_date DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([':query' => $search_term]);
        $search_results = $stmt->fetchAll();

        // إذا لم نجد نتائج بـ FULLTEXT، نستخدم LIKE
        if (empty($search_results)) {
            $sql = "SELECT *, 0 as relevance_score FROM documents WHERE
                    title LIKE :query OR
                    content LIKE :query OR
                    original_name LIKE :query
                    ORDER BY
                        CASE
                            WHEN title LIKE :query THEN 1
                            WHEN original_name LIKE :query THEN 2
                            WHEN content LIKE :query THEN 3
                            ELSE 4
                        END,
                        upload_date DESC";

            $stmt = $pdo->prepare($sql);
            $search_param = '%' . $search_term . '%';
            $stmt->execute([':query' => $search_param]);
            $search_results = $stmt->fetchAll();
        }

        $search_count = count($search_results);
        $execution_time = microtime(true) - $start_time;

        // حفظ إحصائية البحث مع وقت التنفيذ
        $stmt = $pdo->prepare("INSERT INTO searches (search_term, results_count, execution_time) VALUES (?, ?, ?)");
        $stmt->execute([$search_term, $search_count, $execution_time]);

    } catch (Exception $e) {
        // في حالة خطأ في FULLTEXT، استخدم البحث العادي
        $sql = "SELECT *, 0 as relevance_score FROM documents WHERE
                title LIKE :query OR
                content LIKE :query OR
                original_name LIKE :query
                ORDER BY upload_date DESC";

        $stmt = $pdo->prepare($sql);
        $search_param = '%' . $search_term . '%';
        $stmt->execute([':query' => $search_param]);
        $search_results = $stmt->fetchAll();
        $search_count = count($search_results);
        $execution_time = microtime(true) - $start_time;

        // حفظ إحصائية البحث
        $stmt = $pdo->prepare("INSERT INTO searches (search_term, results_count, execution_time) VALUES (?, ?, ?)");
        $stmt->execute([$search_term, $search_count, $execution_time]);
    }
}

// جلب البحثات الشائعة
$stmt = $pdo->query("SELECT search_term, COUNT(*) as count FROM searches GROUP BY search_term ORDER BY count DESC LIMIT 5");
$popular_searches = $stmt->fetchAll();

// دالة لتمييز النص المطابق
function highlightText($text, $query) {
    if (empty($query)) return htmlspecialchars($text);
    
    $highlighted = str_ireplace($query, '<mark>' . $query . '</mark>', htmlspecialchars($text));
    return $highlighted;
}
?>

<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-search"></i> البحث في الوثائق</h2>
        <p class="text-muted">ابحث في أسماء الملفات والمحتوى</p>
    </div>
</div>

<!-- نموذج البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET">
                    <input type="hidden" name="page" value="search">
                    <div class="input-group input-group-lg">
                        <input type="text" class="form-control" name="q" 
                               placeholder="ادخل كلمة البحث..." 
                               value="<?php echo htmlspecialchars($search_query); ?>"
                               required>
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($search_query)): ?>
<!-- نتائج البحث -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>
                    <i class="fas fa-search-plus"></i>
                    نتائج البحث عن: "<?php echo htmlspecialchars($search_query); ?>"
                </h5>
                <small class="text-muted">
                    تم العثور على <?php echo $search_count; ?> نتيجة
                    <?php if (isset($execution_time)): ?>
                        في <?php echo number_format($execution_time, 3); ?> ثانية
                    <?php endif; ?>
                </small>
            </div>
            <div class="card-body">
                <?php if (empty($search_results)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5>لم يتم العثور على نتائج</h5>
                        <p class="text-muted">جرب كلمات بحث مختلفة أو تأكد من الإملاء</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($search_results as $doc): ?>
                        <div class="document-item">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="mb-1">
                                        <a href="index.php?page=view&id=<?php echo $doc['id']; ?>" class="text-decoration-none">
                                            <?php echo highlightText($doc['title'], $search_query); ?>
                                        </a>
                                    </h6>
                                    <p class="mb-1 text-muted">
                                        <?php 
                                        $snippet = substr($doc['content'], 0, 200);
                                        echo highlightText($snippet, $search_query);
                                        if (strlen($doc['content']) > 200) echo '...';
                                        ?>
                                    </p>
                                    <small class="text-muted">
                                        <i class="fas fa-file"></i> <?php echo highlightText($doc['original_name'], $search_query); ?>
                                        | <i class="fas fa-hdd"></i> <?php echo formatFileSize($doc['file_size']); ?>
                                        | <i class="fas fa-calendar"></i> <?php echo date('Y-m-d', strtotime($doc['upload_date'])); ?>
                                    </small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <span class="category-badge category-<?php echo $doc['category']; ?>">
                                        <?php echo $doc['category']; ?>
                                    </span>
                                    <span class="badge bg-secondary ms-2">
                                        <?php echo strtoupper($doc['file_type']); ?>
                                    </span>
                                    <div class="mt-2">
                                        <a href="index.php?page=view&id=<?php echo $doc['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php else: ?>
<!-- البحثات الشائعة -->
<?php if (!empty($popular_searches)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-fire"></i> البحثات الشائعة</h5>
            </div>
            <div class="card-body">
                <?php foreach ($popular_searches as $search): ?>
                    <a href="index.php?page=search&q=<?php echo urlencode($search['search_term']); ?>" 
                       class="btn btn-outline-primary btn-sm me-2 mb-2">
                        <?php echo htmlspecialchars($search['search_term']); ?>
                        <span class="badge bg-secondary"><?php echo $search['count']; ?></span>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- نصائح البحث -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-lightbulb"></i> نصائح للبحث</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>كيفية البحث:</h6>
                        <ul>
                            <li>ابحث في أسماء الملفات</li>
                            <li>ابحث في محتوى الوثائق</li>
                            <li>استخدم كلمات مفتاحية واضحة</li>
                            <li>جرب كلمات مختلفة إذا لم تجد نتائج</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>أمثلة على البحث:</h6>
                        <ul>
                            <li><code>بحث</code> - للبحث عن كلمة "بحث"</li>
                            <li><code>تقرير مالي</code> - للبحث عن التقارير المالية</li>
                            <li><code>دليل</code> - للبحث عن الأدلة</li>
                            <li><code>جامعة</code> - للبحث عن الوثائق الأكاديمية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
mark {
    background-color: #ffeb3b;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}
</style>

<script>
// تركيز على مربع البحث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="q"]');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});
</script>
