<?php
/**
 * Search Suggestions API
 * Provides search suggestions based on query input
 */

header('Content-Type: application/json');

// Include required files
require_once '../config/database.php';
require_once '../classes/SearchEngine.php';

try {
    // Get query parameter
    $query = $_GET['q'] ?? '';
    
    if (strlen($query) < 2) {
        echo json_encode([]);
        exit;
    }
    
    // Initialize database and search engine
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        echo json_encode([]);
        exit;
    }
    
    $searchEngine = new SearchEngine($db);
    
    // Get suggestions
    $suggestions = $searchEngine->getSearchSuggestions($query, 5);
    
    echo json_encode($suggestions);
    
} catch (Exception $e) {
    error_log("Search suggestions error: " . $e->getMessage());
    echo json_encode([]);
}
?>
