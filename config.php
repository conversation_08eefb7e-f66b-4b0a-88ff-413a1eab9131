<?php
/**
 * إعدادات قاعدة البيانات - مشروع إدارة الوثائق البسيط
 * Simple Document Management System Configuration
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'simple_docs';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// إعدادات الملفات
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 ميجابايت
define('ALLOWED_TYPES', ['pdf', 'doc', 'docx']);

// إنشاء مجلد الرفع إذا لم يكن موجود
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0777, true);
}

// دالة لتنسيق حجم الملف
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// دالة لاستخراج النص من ملف PDF
function extractTextFromPDF($filePath) {
    $text = '';

    // الطريقة الأولى: استخدام pdftotext
    if (function_exists('shell_exec') && !empty(shell_exec('which pdftotext'))) {
        $command = "pdftotext " . escapeshellarg($filePath) . " -";
        $text = shell_exec($command);
        if (!empty(trim($text))) {
            return trim($text);
        }
    }

    // الطريقة الثانية: استخدام مكتبة PDF parser بسيطة
    try {
        $content = file_get_contents($filePath);
        if ($content !== false) {
            // استخراج النص البسيط من PDF
            if (preg_match_all('/\((.*?)\)/', $content, $matches)) {
                $text = implode(' ', $matches[1]);
                $text = preg_replace('/[^\x20-\x7E\x{0600}-\x{06FF}]/u', ' ', $text);
                $text = preg_replace('/\s+/', ' ', trim($text));
                if (strlen($text) > 50) {
                    return $text;
                }
            }

            // طريقة أخرى للاستخراج
            if (preg_match_all('/BT\s*(.*?)\s*ET/s', $content, $matches)) {
                $text = implode(' ', $matches[1]);
                $text = preg_replace('/[^\x20-\x7E\x{0600}-\x{06FF}]/u', ' ', $text);
                $text = preg_replace('/\s+/', ' ', trim($text));
                if (strlen($text) > 50) {
                    return $text;
                }
            }
        }
    } catch (Exception $e) {
        error_log("PDF extraction error: " . $e->getMessage());
    }

    return "تم رفع الملف بنجاح ولكن لم يتم استخراج النص. يمكنك البحث باسم الملف.";
}

// دالة لاستخراج النص من ملف Word
function extractTextFromWord($filePath) {
    $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

    if ($extension == 'docx') {
        // استخراج من DOCX
        if (class_exists('ZipArchive')) {
            $zip = new ZipArchive();
            if ($zip->open($filePath) === TRUE) {
                $xml = $zip->getFromName('word/document.xml');
                if ($xml) {
                    $dom = new DOMDocument();
                    if ($dom->loadXML($xml)) {
                        $text = $dom->textContent;
                        $zip->close();
                        return trim($text);
                    }
                }
                $zip->close();
            }
        }
    } elseif ($extension == 'doc') {
        // استخراج من DOC (طريقة بسيطة)
        try {
            if (function_exists('shell_exec') && !empty(shell_exec('which antiword'))) {
                $command = "antiword " . escapeshellarg($filePath);
                $text = shell_exec($command);
                if (!empty(trim($text))) {
                    return trim($text);
                }
            }

            // طريقة بديلة للـ DOC
            $content = file_get_contents($filePath);
            if ($content !== false) {
                $text = '';
                $content = str_replace(chr(0), ' ', $content);
                $text = preg_replace('/[^\x20-\x7E\x{0600}-\x{06FF}]/u', ' ', $content);
                $text = preg_replace('/\s+/', ' ', trim($text));
                if (strlen($text) > 100) {
                    return substr($text, 0, 5000); // أول 5000 حرف
                }
            }
        } catch (Exception $e) {
            error_log("DOC extraction error: " . $e->getMessage());
        }
    }

    return "تم رفع الملف بنجاح ولكن لم يتم استخراج النص. يمكنك البحث باسم الملف.";
}

// دالة محسنة لتصنيف الوثائق
function classifyDocument($title, $content) {
    $title = mb_strtolower($title, 'UTF-8');
    $content = mb_strtolower($content, 'UTF-8');
    $text = $title . ' ' . $content;

    // كلمات مفتاحية محسنة للتصنيف (عربي وإنجليزي)
    $categories = [
        'أكاديمي' => [
            'بحث', 'دراسة', 'جامعة', 'أطروحة', 'رسالة', 'تحليل', 'نتائج', 'منهجية', 'خلاصة', 'مراجع',
            'research', 'study', 'university', 'thesis', 'analysis', 'results', 'methodology', 'conclusion', 'references',
            'academic', 'scholar', 'paper', 'journal', 'abstract', 'literature', 'hypothesis'
        ],
        'تجاري' => [
            'شركة', 'أعمال', 'تجارة', 'مبيعات', 'ربح', 'استثمار', 'مالي', 'اقتصاد', 'سوق', 'عميل',
            'business', 'company', 'sales', 'profit', 'investment', 'financial', 'economy', 'market', 'client',
            'commercial', 'revenue', 'budget', 'finance', 'corporate', 'enterprise', 'strategy'
        ],
        'قانوني' => [
            'قانون', 'عقد', 'اتفاقية', 'محكمة', 'قضية', 'حقوق', 'التزام', 'مادة', 'فقرة', 'نص',
            'law', 'contract', 'agreement', 'court', 'case', 'rights', 'legal', 'clause', 'article',
            'legislation', 'regulation', 'statute', 'judicial', 'attorney', 'lawyer', 'litigation'
        ],
        'تقني' => [
            'تقنية', 'برمجة', 'نظام', 'تطوير', 'كمبيوتر', 'شبكة', 'تطبيق', 'برنامج', 'خوارزمية', 'قاعدة بيانات',
            'technology', 'programming', 'system', 'development', 'computer', 'network', 'application', 'software',
            'algorithm', 'database', 'code', 'technical', 'engineering', 'digital', 'IT', 'hardware'
        ],
        'طبي' => [
            'طب', 'مريض', 'علاج', 'دواء', 'مستشفى', 'صحة', 'تشخيص', 'طبيب', 'عيادة', 'مرض',
            'medical', 'patient', 'treatment', 'medicine', 'hospital', 'health', 'diagnosis', 'doctor',
            'clinic', 'disease', 'therapy', 'pharmaceutical', 'healthcare', 'clinical', 'surgery'
        ]
    ];

    $scores = [];
    $totalWords = str_word_count($text);

    foreach ($categories as $category => $keywords) {
        $score = 0;
        $matchedKeywords = [];

        foreach ($keywords as $keyword) {
            // البحث عن الكلمة المفتاحية
            $pattern = '/\b' . preg_quote($keyword, '/') . '\b/u';
            $matches = preg_match_all($pattern, $text);
            if ($matches > 0) {
                $score += $matches;
                $matchedKeywords[] = $keyword;
            }
        }

        // حساب النسبة المئوية
        $percentage = $totalWords > 0 ? ($score / $totalWords) * 100 : 0;
        $scores[$category] = [
            'score' => $score,
            'percentage' => $percentage,
            'keywords' => $matchedKeywords
        ];
    }

    // العثور على أفضل تصنيف
    $bestCategory = 'عام';
    $bestScore = 0;

    foreach ($scores as $category => $data) {
        if ($data['score'] > $bestScore && $data['score'] >= 2) { // على الأقل كلمتين مطابقتين
            $bestScore = $data['score'];
            $bestCategory = $category;
        }
    }

    return $bestCategory;
}
?>
