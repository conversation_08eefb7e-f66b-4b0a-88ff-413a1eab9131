<?php
/**
 * إعدادات قاعدة البيانات - مشروع إدارة الوثائق البسيط
 * Simple Document Management System Configuration
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'simple_docs';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// إعدادات الملفات
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 ميجابايت
define('ALLOWED_TYPES', ['pdf', 'doc', 'docx']);

// إنشاء مجلد الرفع إذا لم يكن موجود
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0777, true);
}

// دالة لتنسيق حجم الملف
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// دالة لاستخراج النص من ملف PDF بسيط
function extractTextFromPDF($filePath) {
    // محاولة استخدام pdftotext إذا كان متوفر
    if (function_exists('shell_exec')) {
        $text = shell_exec("pdftotext '$filePath' -");
        if ($text) {
            return $text;
        }
    }
    return "لا يمكن استخراج النص من هذا الملف";
}

// دالة لاستخراج النص من ملف Word
function extractTextFromWord($filePath) {
    $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
    
    if ($extension == 'docx') {
        // محاولة قراءة ملف DOCX
        if (class_exists('ZipArchive')) {
            $zip = new ZipArchive();
            if ($zip->open($filePath) === TRUE) {
                $xml = $zip->getFromName('word/document.xml');
                if ($xml) {
                    $dom = new DOMDocument();
                    $dom->loadXML($xml);
                    return $dom->textContent;
                }
            }
        }
    }
    return "لا يمكن استخراج النص من هذا الملف";
}

// دالة بسيطة لتصنيف الوثائق
function classifyDocument($title, $content) {
    $title = strtolower($title);
    $content = strtolower($content);
    $text = $title . ' ' . $content;
    
    // كلمات مفتاحية للتصنيف
    $categories = [
        'أكاديمي' => ['بحث', 'دراسة', 'جامعة', 'أطروحة', 'رسالة', 'تحليل', 'نتائج'],
        'تجاري' => ['شركة', 'أعمال', 'تجارة', 'مبيعات', 'ربح', 'استثمار', 'مالي'],
        'قانوني' => ['قانون', 'عقد', 'اتفاقية', 'محكمة', 'قضية', 'حقوق', 'التزام'],
        'تقني' => ['تقنية', 'برمجة', 'نظام', 'تطوير', 'كمبيوتر', 'شبكة', 'تطبيق'],
        'طبي' => ['طب', 'مريض', 'علاج', 'دواء', 'مستشفى', 'صحة', 'تشخيص']
    ];
    
    $scores = [];
    foreach ($categories as $category => $keywords) {
        $score = 0;
        foreach ($keywords as $keyword) {
            if (strpos($text, $keyword) !== false) {
                $score++;
            }
        }
        $scores[$category] = $score;
    }
    
    // إرجاع التصنيف الأعلى نقاط
    $maxScore = max($scores);
    if ($maxScore > 0) {
        return array_search($maxScore, $scores);
    }
    
    return 'عام';
}
?>
