#!/bin/bash

# سكريبت تثبيت أدوات استخراج النصوص
# Script to install text extraction tools

echo "🔧 تثبيت أدوات استخراج النصوص..."

# تحديث النظام
echo "📦 تحديث قوائم الحزم..."
sudo apt-get update

# تثبيت pdftotext (من poppler-utils)
echo "📄 تثبيت pdftotext لملفات PDF..."
sudo apt-get install -y poppler-utils

# تثبيت antiword لملفات DOC
echo "📝 تثبيت antiword لملفات Word..."
sudo apt-get install -y antiword

# تثبيت unzip إذا لم يكن موجود
echo "📦 تثبيت unzip..."
sudo apt-get install -y unzip

# تثبيت امتدادات PHP المطلوبة
echo "🐘 تثبيت امتدادات PHP..."
sudo apt-get install -y php-zip php-dom php-mbstring

# إعادة تشغيل Apache
echo "🔄 إعادة تشغيل Apache..."
sudo systemctl restart apache2

echo "✅ تم تثبيت جميع الأدوات بنجاح!"

# اختبار الأدوات
echo "🧪 اختبار الأدوات المثبتة..."

echo "pdftotext: $(which pdftotext)"
echo "antiword: $(which antiword)"
echo "unzip: $(which unzip)"

echo "🎉 التثبيت مكتمل! يمكنك الآن استخراج النصوص من الملفات."
