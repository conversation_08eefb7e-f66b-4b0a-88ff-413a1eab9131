#!/bin/bash

# AWS Deployment Script for Document Analytics System
# This script automates the deployment to AWS EC2 with RDS

set -e

echo "🚀 Starting AWS deployment for Document Analytics System..."

# Configuration
AWS_REGION="us-east-1"
EC2_INSTANCE_TYPE="t3.medium"
RDS_INSTANCE_CLASS="db.t3.micro"
KEY_PAIR_NAME="document-analytics-key"
SECURITY_GROUP_NAME="document-analytics-sg"
DB_NAME="document_analytics"
DB_USERNAME="doc_admin"
DB_PASSWORD=$(openssl rand -base64 32)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if user is logged in to AWS
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

print_status "Creating AWS resources..."

# Create VPC
print_status "Creating VPC..."
VPC_ID=$(aws ec2 create-vpc \
    --cidr-block 10.0.0.0/16 \
    --query 'Vpc.VpcId' \
    --output text \
    --region $AWS_REGION)

aws ec2 create-tags \
    --resources $VPC_ID \
    --tags Key=Name,Value=document-analytics-vpc \
    --region $AWS_REGION

# Create Internet Gateway
print_status "Creating Internet Gateway..."
IGW_ID=$(aws ec2 create-internet-gateway \
    --query 'InternetGateway.InternetGatewayId' \
    --output text \
    --region $AWS_REGION)

aws ec2 attach-internet-gateway \
    --vpc-id $VPC_ID \
    --internet-gateway-id $IGW_ID \
    --region $AWS_REGION

# Create Subnets
print_status "Creating subnets..."
PUBLIC_SUBNET_ID=$(aws ec2 create-subnet \
    --vpc-id $VPC_ID \
    --cidr-block 10.0.1.0/24 \
    --availability-zone ${AWS_REGION}a \
    --query 'Subnet.SubnetId' \
    --output text \
    --region $AWS_REGION)

PRIVATE_SUBNET_ID=$(aws ec2 create-subnet \
    --vpc-id $VPC_ID \
    --cidr-block 10.0.2.0/24 \
    --availability-zone ${AWS_REGION}b \
    --query 'Subnet.SubnetId' \
    --output text \
    --region $AWS_REGION)

# Create Route Table
print_status "Creating route table..."
ROUTE_TABLE_ID=$(aws ec2 create-route-table \
    --vpc-id $VPC_ID \
    --query 'RouteTable.RouteTableId' \
    --output text \
    --region $AWS_REGION)

aws ec2 create-route \
    --route-table-id $ROUTE_TABLE_ID \
    --destination-cidr-block 0.0.0.0/0 \
    --gateway-id $IGW_ID \
    --region $AWS_REGION

aws ec2 associate-route-table \
    --subnet-id $PUBLIC_SUBNET_ID \
    --route-table-id $ROUTE_TABLE_ID \
    --region $AWS_REGION

# Create Security Group
print_status "Creating security group..."
SECURITY_GROUP_ID=$(aws ec2 create-security-group \
    --group-name $SECURITY_GROUP_NAME \
    --description "Security group for Document Analytics System" \
    --vpc-id $VPC_ID \
    --query 'GroupId' \
    --output text \
    --region $AWS_REGION)

# Add security group rules
aws ec2 authorize-security-group-ingress \
    --group-id $SECURITY_GROUP_ID \
    --protocol tcp \
    --port 22 \
    --cidr 0.0.0.0/0 \
    --region $AWS_REGION

aws ec2 authorize-security-group-ingress \
    --group-id $SECURITY_GROUP_ID \
    --protocol tcp \
    --port 80 \
    --cidr 0.0.0.0/0 \
    --region $AWS_REGION

aws ec2 authorize-security-group-ingress \
    --group-id $SECURITY_GROUP_ID \
    --protocol tcp \
    --port 443 \
    --cidr 0.0.0.0/0 \
    --region $AWS_REGION

# Create DB Subnet Group
print_status "Creating DB subnet group..."
aws rds create-db-subnet-group \
    --db-subnet-group-name document-analytics-db-subnet \
    --db-subnet-group-description "DB subnet group for Document Analytics" \
    --subnet-ids $PUBLIC_SUBNET_ID $PRIVATE_SUBNET_ID \
    --region $AWS_REGION

# Create RDS Instance
print_status "Creating RDS instance..."
aws rds create-db-instance \
    --db-instance-identifier document-analytics-db \
    --db-instance-class $RDS_INSTANCE_CLASS \
    --engine mysql \
    --engine-version 8.0.35 \
    --master-username $DB_USERNAME \
    --master-user-password $DB_PASSWORD \
    --allocated-storage 20 \
    --db-name $DB_NAME \
    --vpc-security-group-ids $SECURITY_GROUP_ID \
    --db-subnet-group-name document-analytics-db-subnet \
    --backup-retention-period 7 \
    --storage-encrypted \
    --region $AWS_REGION

print_status "Waiting for RDS instance to be available..."
aws rds wait db-instance-available \
    --db-instance-identifier document-analytics-db \
    --region $AWS_REGION

# Get RDS endpoint
RDS_ENDPOINT=$(aws rds describe-db-instances \
    --db-instance-identifier document-analytics-db \
    --query 'DBInstances[0].Endpoint.Address' \
    --output text \
    --region $AWS_REGION)

# Create Key Pair
print_status "Creating key pair..."
aws ec2 create-key-pair \
    --key-name $KEY_PAIR_NAME \
    --query 'KeyMaterial' \
    --output text \
    --region $AWS_REGION > ${KEY_PAIR_NAME}.pem

chmod 400 ${KEY_PAIR_NAME}.pem

# Get latest Amazon Linux 2 AMI
AMI_ID=$(aws ec2 describe-images \
    --owners amazon \
    --filters "Name=name,Values=amzn2-ami-hvm-*-x86_64-gp2" \
    --query 'Images | sort_by(@, &CreationDate) | [-1].ImageId' \
    --output text \
    --region $AWS_REGION)

# Create User Data script
cat > user-data.sh << EOF
#!/bin/bash
yum update -y
yum install -y httpd php php-mysqlnd php-gd php-zip git

# Install Composer
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer

# Start Apache
systemctl start httpd
systemctl enable httpd

# Clone repository
cd /var/www/html
git clone https://github.com/yourusername/document-analytics-system.git .

# Set permissions
chown -R apache:apache /var/www/html
chmod -R 755 /var/www/html
mkdir -p uploads/documents
chmod -R 777 uploads

# Configure database connection
cat > config/database.php << EOL
<?php
class Database {
    private \$host = '$RDS_ENDPOINT';
    private \$db_name = '$DB_NAME';
    private \$username = '$DB_USERNAME';
    private \$password = '$DB_PASSWORD';
    private \$conn;
    
    public function getConnection() {
        \$this->conn = null;
        try {
            \$this->conn = new PDO(
                "mysql:host=" . \$this->host . ";dbname=" . \$this->db_name,
                \$this->username,
                \$this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException \$exception) {
            echo "Connection error: " . \$exception->getMessage();
        }
        return \$this->conn;
    }
    
    public function closeConnection() {
        \$this->conn = null;
    }
}
EOL

# Import database schema
mysql -h $RDS_ENDPOINT -u $DB_USERNAME -p$DB_PASSWORD $DB_NAME < database/schema.sql

# Restart Apache
systemctl restart httpd
EOF

# Launch EC2 Instance
print_status "Launching EC2 instance..."
INSTANCE_ID=$(aws ec2 run-instances \
    --image-id $AMI_ID \
    --count 1 \
    --instance-type $EC2_INSTANCE_TYPE \
    --key-name $KEY_PAIR_NAME \
    --security-group-ids $SECURITY_GROUP_ID \
    --subnet-id $PUBLIC_SUBNET_ID \
    --associate-public-ip-address \
    --user-data file://user-data.sh \
    --query 'Instances[0].InstanceId' \
    --output text \
    --region $AWS_REGION)

aws ec2 create-tags \
    --resources $INSTANCE_ID \
    --tags Key=Name,Value=document-analytics-server \
    --region $AWS_REGION

print_status "Waiting for instance to be running..."
aws ec2 wait instance-running \
    --instance-ids $INSTANCE_ID \
    --region $AWS_REGION

# Get public IP
PUBLIC_IP=$(aws ec2 describe-instances \
    --instance-ids $INSTANCE_ID \
    --query 'Reservations[0].Instances[0].PublicIpAddress' \
    --output text \
    --region $AWS_REGION)

# Clean up
rm user-data.sh

print_status "Deployment completed successfully!"
echo ""
echo "📋 Deployment Summary:"
echo "======================"
echo "🌐 Application URL: http://$PUBLIC_IP"
echo "🗄️  Database Endpoint: $RDS_ENDPOINT"
echo "🔑 SSH Key: ${KEY_PAIR_NAME}.pem"
echo "🖥️  Instance ID: $INSTANCE_ID"
echo "🔒 Security Group: $SECURITY_GROUP_ID"
echo ""
echo "📝 Database Credentials:"
echo "Username: $DB_USERNAME"
echo "Password: $DB_PASSWORD"
echo ""
echo "🔗 SSH Command:"
echo "ssh -i ${KEY_PAIR_NAME}.pem ec2-user@$PUBLIC_IP"
echo ""
print_warning "Please save the database password securely!"
print_status "The application will be available in a few minutes after the instance finishes initializing."
