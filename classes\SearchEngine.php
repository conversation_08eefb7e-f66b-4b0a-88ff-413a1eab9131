<?php
/**
 * Search Engine Class
 * Handles document searching with keyword highlighting and performance tracking
 */

class SearchEngine {
    private $db;
    private $performanceTracker;
    
    public function __construct($database) {
        $this->db = $database;
        $this->performanceTracker = new PerformanceTracker($database);
    }
    
    /**
     * Search documents by keywords
     * @param string $query - Search query
     * @param array $filters - Additional filters (file_type, classification, date_range)
     * @param int $page - Page number for pagination
     * @param int $limit - Results per page
     * @return array - Search results with metadata
     */
    public function searchDocuments($query, $filters = [], $page = 1, $limit = 10) {
        $startTime = microtime(true);
        
        try {
            $offset = ($page - 1) * $limit;
            $searchTerms = $this->prepareSearchTerms($query);
            
            // Build search query
            $sql = $this->buildSearchQuery($searchTerms, $filters);
            $params = $this->buildSearchParams($searchTerms, $filters, $limit, $offset);
            
            // Execute search
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $results = $stmt->fetchAll();
            
            // Get total count for pagination
            $totalCount = $this->getSearchResultsCount($searchTerms, $filters);
            
            // Process results with highlighting
            $processedResults = $this->processSearchResults($results, $searchTerms);
            
            // Track search performance
            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            $this->trackSearchPerformance($query, count($processedResults), $executionTime);
            
            return [
                'results' => $processedResults,
                'total_count' => $totalCount,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($totalCount / $limit),
                'execution_time' => $executionTime,
                'search_terms' => $searchTerms
            ];
            
        } catch (Exception $e) {
            error_log("Search error: " . $e->getMessage());
            return [
                'results' => [],
                'total_count' => 0,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => 0,
                'execution_time' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Prepare search terms from query
     * @param string $query
     * @return array
     */
    private function prepareSearchTerms($query) {
        // Clean and split the query
        $query = trim($query);
        $terms = preg_split('/\s+/', $query);
        
        // Remove empty terms and duplicates
        $terms = array_filter($terms, function($term) {
            return strlen(trim($term)) > 2;
        });
        
        return array_unique($terms);
    }
    
    /**
     * Build search SQL query
     * @param array $searchTerms
     * @param array $filters
     * @return string
     */
    private function buildSearchQuery($searchTerms, $filters) {
        $sql = "SELECT d.id, d.filename, d.original_filename, d.title, d.file_size, 
                       d.file_type, d.classification, d.classification_confidence, 
                       d.upload_date, d.extracted_text,
                       MATCH(d.content, d.extracted_text, d.title) AGAINST(:search_query IN NATURAL LANGUAGE MODE) as relevance_score
                FROM documents d 
                WHERE d.status = 'completed'";
        
        // Add fulltext search condition
        if (!empty($searchTerms)) {
            $sql .= " AND MATCH(d.content, d.extracted_text, d.title) AGAINST(:search_query IN NATURAL LANGUAGE MODE)";
        }
        
        // Add filters
        if (!empty($filters['file_type'])) {
            $sql .= " AND d.file_type = :file_type";
        }
        
        if (!empty($filters['classification'])) {
            $sql .= " AND d.classification = :classification";
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND d.upload_date >= :date_from";
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND d.upload_date <= :date_to";
        }
        
        // Order by relevance and date
        $sql .= " ORDER BY relevance_score DESC, d.upload_date DESC";
        $sql .= " LIMIT :limit OFFSET :offset";
        
        return $sql;
    }
    
    /**
     * Build search parameters
     * @param array $searchTerms
     * @param array $filters
     * @param int $limit
     * @param int $offset
     * @return array
     */
    private function buildSearchParams($searchTerms, $filters, $limit, $offset) {
        $params = [
            ':limit' => $limit,
            ':offset' => $offset
        ];
        
        if (!empty($searchTerms)) {
            $params[':search_query'] = implode(' ', $searchTerms);
        }
        
        if (!empty($filters['file_type'])) {
            $params[':file_type'] = $filters['file_type'];
        }
        
        if (!empty($filters['classification'])) {
            $params[':classification'] = $filters['classification'];
        }
        
        if (!empty($filters['date_from'])) {
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $params[':date_to'] = $filters['date_to'];
        }
        
        return $params;
    }
    
    /**
     * Get total count of search results
     * @param array $searchTerms
     * @param array $filters
     * @return int
     */
    private function getSearchResultsCount($searchTerms, $filters) {
        $sql = "SELECT COUNT(*) as total 
                FROM documents d 
                WHERE d.status = 'completed'";
        
        $params = [];
        
        if (!empty($searchTerms)) {
            $sql .= " AND MATCH(d.content, d.extracted_text, d.title) AGAINST(:search_query IN NATURAL LANGUAGE MODE)";
            $params[':search_query'] = implode(' ', $searchTerms);
        }
        
        // Add same filters as main query
        if (!empty($filters['file_type'])) {
            $sql .= " AND d.file_type = :file_type";
            $params[':file_type'] = $filters['file_type'];
        }
        
        if (!empty($filters['classification'])) {
            $sql .= " AND d.classification = :classification";
            $params[':classification'] = $filters['classification'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND d.upload_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND d.upload_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetch();
            return (int)$result['total'];
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * Process search results with highlighting
     * @param array $results
     * @param array $searchTerms
     * @return array
     */
    private function processSearchResults($results, $searchTerms) {
        $processedResults = [];
        
        foreach ($results as $result) {
            // Create snippet with highlighted terms
            $snippet = $this->createSnippet($result['extracted_text'], $searchTerms);
            $highlightedTitle = $this->highlightText($result['title'], $searchTerms);
            
            $processedResults[] = [
                'id' => $result['id'],
                'filename' => $result['filename'],
                'original_filename' => $result['original_filename'],
                'title' => $result['title'],
                'highlighted_title' => $highlightedTitle,
                'file_size' => $result['file_size'],
                'file_type' => $result['file_type'],
                'classification' => $result['classification'],
                'classification_confidence' => $result['classification_confidence'],
                'upload_date' => $result['upload_date'],
                'snippet' => $snippet,
                'relevance_score' => $result['relevance_score'] ?? 0
            ];
        }
        
        return $processedResults;
    }
    
    /**
     * Create text snippet with highlighted search terms
     * @param string $text
     * @param array $searchTerms
     * @param int $snippetLength
     * @return string
     */
    private function createSnippet($text, $searchTerms, $snippetLength = 300) {
        if (empty($text) || empty($searchTerms)) {
            return substr($text, 0, $snippetLength) . '...';
        }
        
        // Find the first occurrence of any search term
        $firstPosition = false;
        foreach ($searchTerms as $term) {
            $pos = stripos($text, $term);
            if ($pos !== false && ($firstPosition === false || $pos < $firstPosition)) {
                $firstPosition = $pos;
            }
        }
        
        if ($firstPosition === false) {
            // No terms found, return beginning of text
            return substr($text, 0, $snippetLength) . '...';
        }
        
        // Calculate snippet start position
        $start = max(0, $firstPosition - 100);
        $snippet = substr($text, $start, $snippetLength);
        
        // Add ellipsis if needed
        if ($start > 0) {
            $snippet = '...' . $snippet;
        }
        if (strlen($text) > $start + $snippetLength) {
            $snippet .= '...';
        }
        
        // Highlight search terms
        return $this->highlightText($snippet, $searchTerms);
    }
    
    /**
     * Highlight search terms in text
     * @param string $text
     * @param array $searchTerms
     * @return string
     */
    private function highlightText($text, $searchTerms) {
        if (empty($searchTerms)) {
            return $text;
        }
        
        foreach ($searchTerms as $term) {
            $pattern = '/(' . preg_quote($term, '/') . ')/i';
            $replacement = '<span class="' . Config::HIGHLIGHT_CLASS . '">$1</span>';
            $text = preg_replace($pattern, $replacement, $text);
        }
        
        return $text;
    }
    
    /**
     * Track search performance
     * @param string $query
     * @param int $resultsCount
     * @param float $executionTime
     */
    private function trackSearchPerformance($query, $resultsCount, $executionTime) {
        try {
            // Save to search history
            $sql = "INSERT INTO search_history (search_query, results_count, execution_time, user_ip) 
                    VALUES (:query, :results_count, :execution_time, :user_ip)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':query' => $query,
                ':results_count' => $resultsCount,
                ':execution_time' => $executionTime,
                ':user_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            
            // Update total searches count
            $sql = "UPDATE system_statistics SET stat_value = stat_value + 1 WHERE stat_name = 'total_searches'";
            $this->db->exec($sql);
            
            // Track with performance tracker
            $this->performanceTracker->trackOperation('search', $executionTime, $resultsCount);
            
        } catch (Exception $e) {
            error_log("Search tracking error: " . $e->getMessage());
        }
    }
    
    /**
     * Get search suggestions based on previous searches
     * @param string $partial
     * @param int $limit
     * @return array
     */
    public function getSearchSuggestions($partial, $limit = 5) {
        try {
            $sql = "SELECT DISTINCT search_query, COUNT(*) as frequency 
                    FROM search_history 
                    WHERE search_query LIKE :partial 
                    GROUP BY search_query 
                    ORDER BY frequency DESC, search_query ASC 
                    LIMIT :limit";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':partial' => '%' . $partial . '%',
                ':limit' => $limit
            ]);
            
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get popular search terms
     * @param int $limit
     * @return array
     */
    public function getPopularSearchTerms($limit = 10) {
        try {
            $sql = "SELECT search_query, COUNT(*) as frequency 
                    FROM search_history 
                    WHERE search_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    GROUP BY search_query 
                    ORDER BY frequency DESC 
                    LIMIT :limit";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':limit' => $limit]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Advanced search with multiple criteria
     * @param array $criteria
     * @return array
     */
    public function advancedSearch($criteria) {
        $startTime = microtime(true);
        
        try {
            $sql = "SELECT d.*, 
                           MATCH(d.content, d.extracted_text, d.title) AGAINST(:search_query IN NATURAL LANGUAGE MODE) as relevance_score
                    FROM documents d 
                    WHERE d.status = 'completed'";
            
            $params = [];
            
            // Title search
            if (!empty($criteria['title'])) {
                $sql .= " AND d.title LIKE :title";
                $params[':title'] = '%' . $criteria['title'] . '%';
            }
            
            // Content search
            if (!empty($criteria['content'])) {
                $sql .= " AND MATCH(d.content, d.extracted_text) AGAINST(:search_query IN NATURAL LANGUAGE MODE)";
                $params[':search_query'] = $criteria['content'];
            }
            
            // File type filter
            if (!empty($criteria['file_type'])) {
                $sql .= " AND d.file_type = :file_type";
                $params[':file_type'] = $criteria['file_type'];
            }
            
            // Classification filter
            if (!empty($criteria['classification'])) {
                $sql .= " AND d.classification = :classification";
                $params[':classification'] = $criteria['classification'];
            }
            
            // Date range
            if (!empty($criteria['date_from'])) {
                $sql .= " AND d.upload_date >= :date_from";
                $params[':date_from'] = $criteria['date_from'];
            }
            
            if (!empty($criteria['date_to'])) {
                $sql .= " AND d.upload_date <= :date_to";
                $params[':date_to'] = $criteria['date_to'];
            }
            
            // File size range
            if (!empty($criteria['size_min'])) {
                $sql .= " AND d.file_size >= :size_min";
                $params[':size_min'] = $criteria['size_min'];
            }
            
            if (!empty($criteria['size_max'])) {
                $sql .= " AND d.file_size <= :size_max";
                $params[':size_max'] = $criteria['size_max'];
            }
            
            $sql .= " ORDER BY relevance_score DESC, d.upload_date DESC";
            
            // Add pagination
            $page = $criteria['page'] ?? 1;
            $limit = $criteria['limit'] ?? 10;
            $offset = ($page - 1) * $limit;
            
            $sql .= " LIMIT :limit OFFSET :offset";
            $params[':limit'] = $limit;
            $params[':offset'] = $offset;
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $results = $stmt->fetchAll();
            
            $endTime = microtime(true);
            $executionTime = $endTime - $startTime;
            
            return [
                'results' => $results,
                'execution_time' => $executionTime,
                'page' => $page,
                'limit' => $limit
            ];
            
        } catch (Exception $e) {
            error_log("Advanced search error: " . $e->getMessage());
            return ['results' => [], 'execution_time' => 0, 'error' => $e->getMessage()];
        }
    }
}
?>
