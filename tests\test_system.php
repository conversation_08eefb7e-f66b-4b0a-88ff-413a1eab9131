<?php
/**
 * System Testing Script
 * Comprehensive testing for Document Analytics System
 */

// Include required files
require_once '../config/database.php';
require_once '../classes/DocumentManager.php';
require_once '../classes/SearchEngine.php';
require_once '../classes/ClassificationEngine.php';
require_once '../classes/PerformanceTracker.php';

class SystemTester {
    private $db;
    private $testResults = [];
    private $startTime;
    
    public function __construct() {
        $this->startTime = microtime(true);
        echo "🧪 Starting System Tests for Document Analytics System\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
        
        // Initialize database connection
        $database = new Database();
        $this->db = $database->getConnection();
        
        if (!$this->db) {
            $this->fail("Database connection failed");
            exit(1);
        }
    }
    
    public function runAllTests() {
        $this->testDatabaseConnection();
        $this->testDocumentManager();
        $this->testSearchEngine();
        $this->testClassificationEngine();
        $this->testPerformanceTracker();
        $this->testFileOperations();
        $this->testSecurityFeatures();
        
        $this->printResults();
    }
    
    private function testDatabaseConnection() {
        $this->startTest("Database Connection");
        
        try {
            // Test basic connection
            $stmt = $this->db->query("SELECT 1");
            $this->pass("Basic database connection");
            
            // Test required tables exist
            $tables = ['documents', 'search_history', 'classification_rules', 'performance_metrics'];
            foreach ($tables as $table) {
                $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    $this->pass("Table '$table' exists");
                } else {
                    $this->fail("Table '$table' missing");
                }
            }
            
            // Test indexes
            $stmt = $this->db->query("SHOW INDEX FROM documents WHERE Key_name = 'ft_content'");
            if ($stmt->rowCount() > 0) {
                $this->pass("Full-text index exists");
            } else {
                $this->fail("Full-text index missing");
            }
            
        } catch (Exception $e) {
            $this->fail("Database test failed: " . $e->getMessage());
        }
    }
    
    private function testDocumentManager() {
        $this->startTest("Document Manager");
        
        try {
            $documentManager = new DocumentManager($this->db);
            
            // Test document retrieval
            $documents = $documentManager->getDocuments(1, 5);
            $this->pass("Document retrieval works");
            
            // Test document count
            if (is_array($documents)) {
                $this->pass("Documents returned as array");
            } else {
                $this->fail("Documents not returned as array");
            }
            
            // Test sorting functionality
            $sortedDocs = $documentManager->getDocuments(1, 5, 'title', 'ASC');
            $this->pass("Document sorting works");
            
        } catch (Exception $e) {
            $this->fail("DocumentManager test failed: " . $e->getMessage());
        }
    }
    
    private function testSearchEngine() {
        $this->startTest("Search Engine");
        
        try {
            $searchEngine = new SearchEngine($this->db);
            
            // Test basic search
            $results = $searchEngine->searchDocuments("test", [], 1, 5);
            if (isset($results['results']) && is_array($results['results'])) {
                $this->pass("Basic search functionality");
            } else {
                $this->fail("Search results format incorrect");
            }
            
            // Test search suggestions
            $suggestions = $searchEngine->getSearchSuggestions("test", 5);
            if (is_array($suggestions)) {
                $this->pass("Search suggestions work");
            } else {
                $this->fail("Search suggestions failed");
            }
            
            // Test popular searches
            $popular = $searchEngine->getPopularSearchTerms(5);
            if (is_array($popular)) {
                $this->pass("Popular search terms retrieval");
            } else {
                $this->fail("Popular search terms failed");
            }
            
        } catch (Exception $e) {
            $this->fail("SearchEngine test failed: " . $e->getMessage());
        }
    }
    
    private function testClassificationEngine() {
        $this->startTest("Classification Engine");
        
        try {
            $classificationEngine = new ClassificationEngine($this->db);
            
            // Test classification rules retrieval
            $rules = $classificationEngine->getClassificationRules();
            if (is_array($rules)) {
                $this->pass("Classification rules retrieval");
            } else {
                $this->fail("Classification rules retrieval failed");
            }
            
            // Test classification statistics
            $stats = $classificationEngine->getClassificationStatistics();
            if (is_array($stats)) {
                $this->pass("Classification statistics");
            } else {
                $this->fail("Classification statistics failed");
            }
            
            // Test adding classification rule
            $result = $classificationEngine->addClassificationRule(
                'test_category',
                'test,keywords',
                1.0,
                'keyword'
            );
            
            if ($result) {
                $this->pass("Add classification rule");
                
                // Clean up test rule
                $this->db->exec("DELETE FROM classification_rules WHERE category = 'test_category'");
            } else {
                $this->fail("Add classification rule failed");
            }
            
        } catch (Exception $e) {
            $this->fail("ClassificationEngine test failed: " . $e->getMessage());
        }
    }
    
    private function testPerformanceTracker() {
        $this->startTest("Performance Tracker");
        
        try {
            $performanceTracker = new PerformanceTracker($this->db);
            
            // Test tracking operation
            $result = $performanceTracker->trackOperation('test', 0.1, 1, ['test' => 'data']);
            if ($result) {
                $this->pass("Performance tracking");
            } else {
                $this->fail("Performance tracking failed");
            }
            
            // Test getting statistics
            $stats = $performanceTracker->getSystemStats(30);
            if (is_array($stats)) {
                $this->pass("Performance statistics retrieval");
            } else {
                $this->fail("Performance statistics failed");
            }
            
            // Test performance summary
            $summary = $performanceTracker->getPerformanceSummary();
            if (is_array($summary)) {
                $this->pass("Performance summary");
            } else {
                $this->fail("Performance summary failed");
            }
            
            // Clean up test data
            $this->db->exec("DELETE FROM performance_metrics WHERE operation_type = 'test'");
            
        } catch (Exception $e) {
            $this->fail("PerformanceTracker test failed: " . $e->getMessage());
        }
    }
    
    private function testFileOperations() {
        $this->startTest("File Operations");
        
        try {
            // Test upload directory exists and is writable
            $uploadDir = '../uploads/documents/';
            if (is_dir($uploadDir)) {
                $this->pass("Upload directory exists");
                
                if (is_writable($uploadDir)) {
                    $this->pass("Upload directory is writable");
                } else {
                    $this->fail("Upload directory not writable");
                }
            } else {
                $this->fail("Upload directory missing");
            }
            
            // Test file size limits
            $maxSize = ini_get('upload_max_filesize');
            if ($maxSize) {
                $this->pass("Upload max filesize configured: $maxSize");
            } else {
                $this->fail("Upload max filesize not configured");
            }
            
            // Test required PHP extensions
            $extensions = ['pdo_mysql', 'zip', 'gd'];
            foreach ($extensions as $ext) {
                if (extension_loaded($ext)) {
                    $this->pass("PHP extension '$ext' loaded");
                } else {
                    $this->fail("PHP extension '$ext' missing");
                }
            }
            
        } catch (Exception $e) {
            $this->fail("File operations test failed: " . $e->getMessage());
        }
    }
    
    private function testSecurityFeatures() {
        $this->startTest("Security Features");
        
        try {
            // Test SQL injection protection (basic test)
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM documents WHERE id = ?");
            $stmt->execute(["'; DROP TABLE documents; --"]);
            $this->pass("SQL injection protection (prepared statements)");
            
            // Test XSS protection
            $testString = "<script>alert('xss')</script>";
            $escaped = htmlspecialchars($testString);
            if ($escaped !== $testString) {
                $this->pass("XSS protection (htmlspecialchars)");
            } else {
                $this->fail("XSS protection not working");
            }
            
            // Test file upload restrictions
            $allowedTypes = Config::ALLOWED_EXTENSIONS;
            if (is_array($allowedTypes) && !empty($allowedTypes)) {
                $this->pass("File type restrictions configured");
            } else {
                $this->fail("File type restrictions not configured");
            }
            
            // Test file size limits
            $maxSize = Config::MAX_FILE_SIZE;
            if ($maxSize > 0) {
                $this->pass("File size limits configured");
            } else {
                $this->fail("File size limits not configured");
            }
            
        } catch (Exception $e) {
            $this->fail("Security test failed: " . $e->getMessage());
        }
    }
    
    private function startTest($testName) {
        echo "🔍 Testing: $testName\n";
    }
    
    private function pass($message) {
        echo "  ✅ $message\n";
        $this->testResults[] = ['status' => 'PASS', 'message' => $message];
    }
    
    private function fail($message) {
        echo "  ❌ $message\n";
        $this->testResults[] = ['status' => 'FAIL', 'message' => $message];
    }
    
    private function printResults() {
        $endTime = microtime(true);
        $totalTime = $endTime - $this->startTime;
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 TEST RESULTS SUMMARY\n";
        echo str_repeat("=", 60) . "\n";
        
        $passed = 0;
        $failed = 0;
        
        foreach ($this->testResults as $result) {
            if ($result['status'] === 'PASS') {
                $passed++;
            } else {
                $failed++;
            }
        }
        
        $total = $passed + $failed;
        $passRate = $total > 0 ? ($passed / $total) * 100 : 0;
        
        echo "Total Tests: $total\n";
        echo "Passed: $passed\n";
        echo "Failed: $failed\n";
        echo "Pass Rate: " . number_format($passRate, 1) . "%\n";
        echo "Execution Time: " . number_format($totalTime, 3) . " seconds\n";
        
        if ($failed > 0) {
            echo "\n❌ FAILED TESTS:\n";
            foreach ($this->testResults as $result) {
                if ($result['status'] === 'FAIL') {
                    echo "  - " . $result['message'] . "\n";
                }
            }
        }
        
        echo "\n";
        if ($passRate >= 90) {
            echo "🎉 EXCELLENT! System is ready for deployment.\n";
        } elseif ($passRate >= 75) {
            echo "⚠️  GOOD! Minor issues need attention.\n";
        } else {
            echo "🚨 CRITICAL! Major issues must be fixed before deployment.\n";
        }
        
        echo str_repeat("=", 60) . "\n";
    }
}

// Run tests
$tester = new SystemTester();
$tester->runAllTests();
?>
