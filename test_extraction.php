<?php
/**
 * اختبار استخراج النصوص من الملفات
 * Test Text Extraction from Files
 */

require_once 'config.php';

echo "<h2>اختبار استخراج النصوص</h2>";

// اختبار ملفات PDF
echo "<h3>اختبار ملفات PDF:</h3>";
$pdfFiles = glob('uploads/*.pdf');
foreach ($pdfFiles as $file) {
    echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
    echo "<strong>الملف:</strong> " . basename($file) . "<br>";
    echo "<strong>الحجم:</strong> " . formatFileSize(filesize($file)) . "<br>";
    
    $text = extractTextFromPDF($file);
    echo "<strong>النص المستخرج:</strong><br>";
    echo "<div style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
    echo nl2br(htmlspecialchars(substr($text, 0, 1000)));
    if (strlen($text) > 1000) echo "...";
    echo "</div>";
    echo "</div>";
}

// اختبار ملفات Word
echo "<h3>اختبار ملفات Word:</h3>";
$wordFiles = array_merge(glob('uploads/*.doc'), glob('uploads/*.docx'));
foreach ($wordFiles as $file) {
    echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
    echo "<strong>الملف:</strong> " . basename($file) . "<br>";
    echo "<strong>الحجم:</strong> " . formatFileSize(filesize($file)) . "<br>";
    
    $text = extractTextFromWord($file);
    echo "<strong>النص المستخرج:</strong><br>";
    echo "<div style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
    echo nl2br(htmlspecialchars(substr($text, 0, 1000)));
    if (strlen($text) > 1000) echo "...";
    echo "</div>";
    echo "</div>";
}

// اختبار التصنيف
echo "<h3>اختبار التصنيف:</h3>";
$testTexts = [
    'بحث في علوم الحاسوب والذكاء الاصطناعي في الجامعة',
    'عقد بيع وشراء بين الطرفين وفقاً للقانون المدني',
    'تقرير مالي سنوي للشركة وأرباحها وخسائرها',
    'دليل المستخدم لنظام إدارة قواعد البيانات',
    'تقرير طبي عن حالة المريض وتشخيص المرض'
];

foreach ($testTexts as $text) {
    $category = classifyDocument('', $text);
    echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
    echo "<strong>النص:</strong> " . htmlspecialchars($text) . "<br>";
    echo "<strong>التصنيف:</strong> <span style='background: #e3f2fd; padding: 3px 8px; border-radius: 15px;'>" . $category . "</span>";
    echo "</div>";
}

echo "<h3>معلومات النظام:</h3>";
echo "<ul>";
echo "<li>إصدار PHP: " . PHP_VERSION . "</li>";
echo "<li>امتداد ZIP: " . (extension_loaded('zip') ? 'متوفر' : 'غير متوفر') . "</li>";
echo "<li>امتداد DOM: " . (extension_loaded('dom') ? 'متوفر' : 'غير متوفر') . "</li>";
echo "<li>shell_exec: " . (function_exists('shell_exec') ? 'متوفر' : 'غير متوفر') . "</li>";
echo "<li>pdftotext: " . (!empty(shell_exec('which pdftotext 2>/dev/null')) ? 'متوفر' : 'غير متوفر') . "</li>";
echo "<li>antiword: " . (!empty(shell_exec('which antiword 2>/dev/null')) ? 'متوفر' : 'غير متوفر') . "</li>";
echo "</ul>";
?>
