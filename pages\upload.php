<?php
/**
 * Document Upload Page
 * Handles file uploads and displays upload interface
 */

$message = '';
$messageType = '';

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['documents'])) {
    $documentManager = new DocumentManager($db);
    $uploadResults = [];
    
    // Handle multiple file uploads
    $files = $_FILES['documents'];
    $fileCount = count($files['name']);
    
    for ($i = 0; $i < $fileCount; $i++) {
        if ($files['error'][$i] === UPLOAD_ERR_OK) {
            $file = [
                'name' => $files['name'][$i],
                'type' => $files['type'][$i],
                'tmp_name' => $files['tmp_name'][$i],
                'error' => $files['error'][$i],
                'size' => $files['size'][$i]
            ];
            
            $result = $documentManager->uploadDocument($file);
            $uploadResults[] = [
                'filename' => $file['name'],
                'result' => $result
            ];
        }
    }
    
    // Prepare summary message
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    
    foreach ($uploadResults as $upload) {
        if ($upload['result']['success']) {
            $successCount++;
        } else {
            $errorCount++;
            $errors[] = $upload['filename'] . ': ' . $upload['result']['message'];
        }
    }
    
    if ($successCount > 0) {
        $message = "Successfully uploaded $successCount document(s).";
        $messageType = 'success';
        
        if ($errorCount > 0) {
            $message .= " $errorCount document(s) failed to upload.";
            $messageType = 'warning';
        }
    } else {
        $message = "Failed to upload documents. " . implode(' ', $errors);
        $messageType = 'danger';
    }
}

// Get upload statistics
try {
    $sql = "SELECT 
                COUNT(*) as total_uploads,
                SUM(file_size) as total_size,
                AVG(file_size) as avg_size
            FROM documents 
            WHERE upload_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
    $stmt = $db->query($sql);
    $uploadStats = $stmt->fetch();
} catch (Exception $e) {
    $uploadStats = ['total_uploads' => 0, 'total_size' => 0, 'avg_size' => 0];
}

function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-upload"></i> Upload Documents
        </h1>
    </div>
</div>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <!-- Upload Form -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cloud-upload-alt"></i> Upload New Documents
                </h5>
            </div>
            <div class="card-body">
                <form id="uploadForm" method="POST" enctype="multipart/form-data">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h4>Drag & Drop Files Here</h4>
                        <p class="text-muted">or click to browse files</p>
                        <input type="file" 
                               id="fileInput" 
                               name="documents[]" 
                               multiple 
                               accept=".pdf,.doc,.docx"
                               style="display: none;">
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Supported formats: PDF, DOC, DOCX | Max size: <?php echo formatFileSize(Config::MAX_FILE_SIZE); ?> per file
                            </small>
                        </div>
                    </div>
                    
                    <!-- File List -->
                    <div id="fileList" class="mt-4" style="display: none;">
                        <h6>Selected Files:</h6>
                        <div id="selectedFiles"></div>
                    </div>
                    
                    <!-- Upload Progress -->
                    <div id="uploadProgress" class="mt-4" style="display: none;">
                        <h6>Upload Progress:</h6>
                        <div class="progress">
                            <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%">
                                0%
                            </div>
                        </div>
                        <div id="uploadStatus" class="mt-2"></div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="submit" id="uploadBtn" class="btn btn-primary" disabled>
                            <i class="fas fa-upload"></i> Upload Documents
                        </button>
                        <button type="button" id="clearBtn" class="btn btn-secondary" onclick="clearFiles()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Upload Statistics -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Upload Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Documents (30 days):</span>
                        <strong><?php echo number_format($uploadStats['total_uploads']); ?></strong>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Total Size:</span>
                        <strong><?php echo formatFileSize($uploadStats['total_size']); ?></strong>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Average Size:</span>
                        <strong><?php echo formatFileSize($uploadStats['avg_size']); ?></strong>
                    </div>
                </div>
                
                <hr>
                
                <h6>Supported File Types:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-pdf text-danger"></i> PDF Documents</li>
                    <li><i class="fas fa-file-word text-primary"></i> Word Documents (.doc, .docx)</li>
                </ul>
                
                <h6 class="mt-3">Features:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Automatic text extraction</li>
                    <li><i class="fas fa-check text-success"></i> Title detection</li>
                    <li><i class="fas fa-check text-success"></i> Auto-classification</li>
                    <li><i class="fas fa-check text-success"></i> Keyword indexing</li>
                </ul>
            </div>
        </div>
        
        <!-- Recent Uploads -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Recent Uploads
                </h5>
            </div>
            <div class="card-body">
                <?php
                try {
                    $sql = "SELECT title, file_type, upload_date, file_size 
                            FROM documents 
                            WHERE status = 'completed' 
                            ORDER BY upload_date DESC 
                            LIMIT 5";
                    $stmt = $db->query($sql);
                    $recentUploads = $stmt->fetchAll();
                } catch (Exception $e) {
                    $recentUploads = [];
                }
                ?>
                
                <?php if (empty($recentUploads)): ?>
                    <p class="text-muted text-center">No recent uploads</p>
                <?php else: ?>
                    <?php foreach ($recentUploads as $upload): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                        <div>
                            <div class="fw-bold">
                                <?php echo htmlspecialchars(substr($upload['title'], 0, 30)); ?>
                                <?php if (strlen($upload['title']) > 30) echo '...'; ?>
                            </div>
                            <small class="text-muted">
                                <?php echo strtoupper($upload['file_type']); ?> • 
                                <?php echo formatFileSize($upload['file_size']); ?>
                            </small>
                        </div>
                        <small class="text-muted">
                            <?php echo date('M j', strtotime($upload['upload_date'])); ?>
                        </small>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileList = document.getElementById('fileList');
    const selectedFiles = document.getElementById('selectedFiles');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadForm = document.getElementById('uploadForm');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const uploadStatus = document.getElementById('uploadStatus');
    
    let files = [];
    
    // Click to browse files
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // Drag and drop functionality
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });
    
    // File input change
    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });
    
    function handleFiles(newFiles) {
        for (let file of newFiles) {
            if (isValidFile(file)) {
                files.push(file);
            }
        }
        updateFileList();
        updateUploadButton();
    }
    
    function isValidFile(file) {
        const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        const maxSize = <?php echo Config::MAX_FILE_SIZE; ?>;
        
        if (!allowedTypes.includes(file.type)) {
            alert('File type not supported: ' + file.name);
            return false;
        }
        
        if (file.size > maxSize) {
            alert('File too large: ' + file.name);
            return false;
        }
        
        return true;
    }
    
    function updateFileList() {
        if (files.length === 0) {
            fileList.style.display = 'none';
            return;
        }
        
        fileList.style.display = 'block';
        selectedFiles.innerHTML = '';
        
        files.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'alert alert-info d-flex justify-content-between align-items-center';
            fileItem.innerHTML = `
                <div>
                    <i class="fas fa-file"></i>
                    <strong>${file.name}</strong>
                    <small class="text-muted">(${formatFileSize(file.size)})</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            selectedFiles.appendChild(fileItem);
        });
    }
    
    function updateUploadButton() {
        uploadBtn.disabled = files.length === 0;
    }
    
    window.removeFile = function(index) {
        files.splice(index, 1);
        updateFileList();
        updateUploadButton();
    };
    
    window.clearFiles = function() {
        files = [];
        fileInput.value = '';
        updateFileList();
        updateUploadButton();
    };
    
    function formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return size.toFixed(2) + ' ' + units[unitIndex];
    }
    
    // Form submission with progress
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (files.length === 0) return;
        
        const formData = new FormData();
        files.forEach(file => {
            formData.append('documents[]', file);
        });
        
        uploadProgress.style.display = 'block';
        uploadBtn.disabled = true;
        
        const xhr = new XMLHttpRequest();
        
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressBar.style.width = percentComplete + '%';
                progressBar.textContent = Math.round(percentComplete) + '%';
            }
        });
        
        xhr.addEventListener('load', () => {
            if (xhr.status === 200) {
                uploadStatus.innerHTML = '<div class="alert alert-success">Upload completed successfully!</div>';
                setTimeout(() => {
                    location.reload();
                }, 2000);
            } else {
                uploadStatus.innerHTML = '<div class="alert alert-danger">Upload failed. Please try again.</div>';
                uploadBtn.disabled = false;
            }
        });
        
        xhr.addEventListener('error', () => {
            uploadStatus.innerHTML = '<div class="alert alert-danger">Upload error. Please try again.</div>';
            uploadBtn.disabled = false;
        });
        
        xhr.open('POST', window.location.href);
        xhr.send(formData);
    });
});
</script>
