<?php
/**
 * Dashboard Page
 * Main overview of the document analytics system
 */

// Get system statistics
try {
    $sql = "SELECT stat_name, stat_value FROM system_statistics";
    $stmt = $db->query($sql);
    $stats = [];
    while ($row = $stmt->fetch()) {
        $stats[$row['stat_name']] = $row['stat_value'];
    }
} catch (Exception $e) {
    $stats = [
        'total_documents' => 0,
        'total_file_size' => 0,
        'total_searches' => 0,
        'total_classifications' => 0
    ];
}

// Get recent documents
try {
    $sql = "SELECT id, title, file_type, classification, upload_date, file_size 
            FROM documents 
            WHERE status = 'completed' 
            ORDER BY upload_date DESC 
            LIMIT 5";
    $stmt = $db->query($sql);
    $recentDocuments = $stmt->fetchAll();
} catch (Exception $e) {
    $recentDocuments = [];
}

// Get classification distribution
try {
    $sql = "SELECT classification, COUNT(*) as count 
            FROM documents 
            WHERE status = 'completed' 
            GROUP BY classification 
            ORDER BY count DESC";
    $stmt = $db->query($sql);
    $classificationStats = $stmt->fetchAll();
} catch (Exception $e) {
    $classificationStats = [];
}

// Get performance summary
$performanceTracker = new PerformanceTracker($db);
$performanceSummary = $performanceTracker->getPerformanceSummary();

// Format file size
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo number_format($stats['total_documents'] ?? 0); ?></div>
            <div class="stat-label">
                <i class="fas fa-file-alt"></i> Total Documents
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #17a2b8, #138496);">
            <div class="stat-number"><?php echo formatFileSize($stats['total_file_size'] ?? 0); ?></div>
            <div class="stat-label">
                <i class="fas fa-hdd"></i> Total Storage
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
            <div class="stat-number"><?php echo number_format($stats['total_searches'] ?? 0); ?></div>
            <div class="stat-label">
                <i class="fas fa-search"></i> Total Searches
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stat-card" style="background: linear-gradient(135deg, #6f42c1, #5a32a3);">
            <div class="stat-number"><?php echo number_format($stats['total_classifications'] ?? 0); ?></div>
            <div class="stat-label">
                <i class="fas fa-tags"></i> Classifications
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Documents -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock"></i> Recent Documents
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentDocuments)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No documents uploaded yet.</p>
                        <a href="index.php?action=upload" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Upload Your First Document
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Type</th>
                                    <th>Classification</th>
                                    <th>Size</th>
                                    <th>Upload Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentDocuments as $doc): ?>
                                <tr>
                                    <td>
                                        <a href="index.php?action=view_document&id=<?php echo $doc['id']; ?>" 
                                           class="document-title">
                                            <?php echo htmlspecialchars(substr($doc['title'], 0, 50)); ?>
                                            <?php if (strlen($doc['title']) > 50) echo '...'; ?>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo strtoupper($doc['file_type']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge classification-<?php echo $doc['classification']; ?>">
                                            <?php echo ucfirst($doc['classification']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatFileSize($doc['file_size']); ?></td>
                                    <td><?php echo date('M j, Y', strtotime($doc['upload_date'])); ?></td>
                                    <td>
                                        <a href="index.php?action=view_document&id=<?php echo $doc['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="index.php?action=browse" class="btn btn-outline-primary">
                            <i class="fas fa-list"></i> View All Documents
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Classification Distribution -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> Classification Distribution
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($classificationStats)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-pie fa-2x text-muted mb-3"></i>
                        <p class="text-muted">No classified documents yet.</p>
                    </div>
                <?php else: ?>
                    <?php 
                    $totalDocs = array_sum(array_column($classificationStats, 'count'));
                    foreach ($classificationStats as $stat): 
                        $percentage = $totalDocs > 0 ? ($stat['count'] / $totalDocs) * 100 : 0;
                    ?>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="badge classification-<?php echo $stat['classification']; ?>">
                                <?php echo ucfirst($stat['classification']); ?>
                            </span>
                            <small class="text-muted">
                                <?php echo $stat['count']; ?> (<?php echo number_format($percentage, 1); ?>%)
                            </small>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar" 
                                 style="width: <?php echo $percentage; ?>%"
                                 aria-valuenow="<?php echo $percentage; ?>" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Performance Summary -->
<?php if (!empty($performanceSummary['recent_stats'])): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> Performance Summary (Last 24 Hours)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($performanceSummary['recent_stats'] as $stat): ?>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title text-capitalize">
                                    <?php echo str_replace('_', ' ', $stat['operation_type']); ?>
                                </h6>
                                <p class="card-text">
                                    <strong><?php echo $stat['count']; ?></strong> operations<br>
                                    <small class="text-muted">
                                        Avg: <?php echo PerformanceTracker::formatExecutionTime($stat['avg_time']); ?>
                                    </small>
                                </p>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="index.php?action=upload" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-upload fa-2x mb-2"></i><br>
                            Upload Documents
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="index.php?action=search" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-search fa-2x mb-2"></i><br>
                            Search Documents
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="index.php?action=browse" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-list fa-2x mb-2"></i><br>
                            Browse All
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="index.php?action=statistics" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                            View Statistics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh dashboard every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>
