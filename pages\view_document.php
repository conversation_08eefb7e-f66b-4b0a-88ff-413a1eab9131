<?php
/**
 * View Document Page
 * Display document details and content with search highlighting
 */

// Get document ID
$documentId = (int)($_GET['id'] ?? 0);
$highlightQuery = $_GET['highlight'] ?? '';

if ($documentId <= 0) {
    header('Location: index.php?action=browse');
    exit;
}

// Initialize document manager
$documentManager = new DocumentManager($db);

// Get document details
$document = $documentManager->getDocumentById($documentId);

if (!$document) {
    header('Location: index.php?action=browse&error=document_not_found');
    exit;
}

// Get document keywords
try {
    $sql = "SELECT keyword, frequency FROM document_keywords 
            WHERE document_id = :document_id 
            ORDER BY frequency DESC 
            LIMIT 20";
    $stmt = $db->prepare($sql);
    $stmt->execute([':document_id' => $documentId]);
    $keywords = $stmt->fetchAll();
} catch (Exception $e) {
    $keywords = [];
}

// Get related documents (same classification)
try {
    $sql = "SELECT id, title, file_type, upload_date 
            FROM documents 
            WHERE classification = :classification 
            AND id != :document_id 
            AND status = 'completed'
            ORDER BY upload_date DESC 
            LIMIT 5";
    $stmt = $db->prepare($sql);
    $stmt->execute([
        ':classification' => $document['classification'],
        ':document_id' => $documentId
    ]);
    $relatedDocuments = $stmt->fetchAll();
} catch (Exception $e) {
    $relatedDocuments = [];
}

// Highlight search terms in content
function highlightText($text, $query) {
    if (empty($query)) {
        return htmlspecialchars($text);
    }
    
    $terms = explode(' ', $query);
    $text = htmlspecialchars($text);
    
    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term)) {
            $pattern = '/(' . preg_quote($term, '/') . ')/i';
            $replacement = '<span class="' . Config::HIGHLIGHT_CLASS . '">$1</span>';
            $text = preg_replace($pattern, $replacement, $text);
        }
    }
    
    return $text;
}

function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>

<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php?action=browse">Browse Documents</a></li>
                <li class="breadcrumb-item active"><?php echo htmlspecialchars($document['title']); ?></li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <!-- Document Content -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h4 class="mb-1"><?php echo htmlspecialchars($document['title']); ?></h4>
                        <small class="text-muted">
                            <i class="fas fa-file"></i> <?php echo htmlspecialchars($document['original_filename']); ?>
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-secondary me-2">
                            <?php echo strtoupper($document['file_type']); ?>
                        </span>
                        <span class="badge classification-<?php echo $document['classification']; ?>">
                            <?php echo ucfirst($document['classification']); ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Document Actions -->
                <div class="mb-3">
                    <div class="btn-group" role="group">
                        <a href="<?php echo htmlspecialchars($document['file_path']); ?>" 
                           class="btn btn-primary" 
                           download="<?php echo htmlspecialchars($document['original_filename']); ?>">
                            <i class="fas fa-download"></i> Download
                        </a>
                        <button type="button" 
                                class="btn btn-outline-danger" 
                                onclick="deleteDocument(<?php echo $document['id']; ?>, '<?php echo htmlspecialchars($document['title']); ?>')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                        <a href="index.php?action=search&q=<?php echo urlencode($document['title']); ?>" 
                           class="btn btn-outline-info">
                            <i class="fas fa-search"></i> Find Similar
                        </a>
                    </div>
                </div>
                
                <!-- Document Content -->
                <div class="document-content">
                    <h6>Document Content:</h6>
                    <?php if (!empty($document['extracted_text'])): ?>
                        <div class="border rounded p-3 bg-light" style="max-height: 500px; overflow-y: auto;">
                            <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">
<?php echo highlightText($document['extracted_text'], $highlightQuery); ?>
                            </pre>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            Text content could not be extracted from this document. 
                            <a href="<?php echo htmlspecialchars($document['file_path']); ?>" 
                               download="<?php echo htmlspecialchars($document['original_filename']); ?>">
                                Download the file
                            </a> to view its contents.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Document Metadata -->
    <div class="col-lg-4 mb-4">
        <!-- Document Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Document Information
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>File Size:</strong></td>
                        <td><?php echo formatFileSize($document['file_size']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>File Type:</strong></td>
                        <td><?php echo strtoupper($document['file_type']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Upload Date:</strong></td>
                        <td><?php echo date('M j, Y g:i A', strtotime($document['upload_date'])); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Classification:</strong></td>
                        <td>
                            <span class="badge classification-<?php echo $document['classification']; ?>">
                                <?php echo ucfirst($document['classification']); ?>
                            </span>
                            <?php if ($document['classification_confidence'] > 0): ?>
                            <br>
                            <small class="text-muted">
                                <?php echo number_format($document['classification_confidence'], 1); ?>% confidence
                            </small>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge bg-success">
                                <?php echo ucfirst($document['status']); ?>
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Keywords -->
        <?php if (!empty($keywords)): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-key"></i> Keywords
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($keywords as $keyword): ?>
                <a href="index.php?action=search&q=<?php echo urlencode($keyword['keyword']); ?>" 
                   class="btn btn-outline-primary btn-sm me-2 mb-2">
                    <?php echo htmlspecialchars($keyword['keyword']); ?>
                    <span class="badge bg-secondary"><?php echo $keyword['frequency']; ?></span>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Related Documents -->
        <?php if (!empty($relatedDocuments)): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-link"></i> Related Documents
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($relatedDocuments as $related): ?>
                <div class="mb-3 pb-2 border-bottom">
                    <div class="fw-bold">
                        <a href="index.php?action=view_document&id=<?php echo $related['id']; ?>">
                            <?php echo htmlspecialchars(substr($related['title'], 0, 50)); ?>
                            <?php if (strlen($related['title']) > 50) echo '...'; ?>
                        </a>
                    </div>
                    <small class="text-muted">
                        <span class="badge bg-secondary"><?php echo strtoupper($related['file_type']); ?></span>
                        <?php echo date('M j, Y', strtotime($related['upload_date'])); ?>
                    </small>
                </div>
                <?php endforeach; ?>
                
                <div class="text-center">
                    <a href="index.php?action=search&classification=<?php echo $document['classification']; ?>" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-search"></i> View All <?php echo ucfirst($document['classification']); ?> Documents
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Search Highlighting Info -->
<?php if ($highlightQuery): ?>
<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-search"></i>
            Showing search results for: <strong>"<?php echo htmlspecialchars($highlightQuery); ?>"</strong>
            <a href="index.php?action=view_document&id=<?php echo $documentId; ?>" class="btn btn-sm btn-outline-primary ms-2">
                View without highlighting
            </a>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function deleteDocument(id, title) {
    if (confirm('Are you sure you want to delete "' + title + '"? This action cannot be undone.')) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'api/delete_document.php';
        
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'document_id';
        idInput.value = id;
        
        form.appendChild(idInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-scroll to first highlighted term
document.addEventListener('DOMContentLoaded', function() {
    const firstHighlight = document.querySelector('.search-highlight');
    if (firstHighlight) {
        firstHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
});
</script>
