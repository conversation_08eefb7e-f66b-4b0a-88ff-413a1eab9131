/**
 * Cloud-Based Document Analytics System
 * Custom Styles
 */

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* Search Highlight */
.search-highlight {
    background-color: #ffeb3b;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #007bff;
    border-radius: 10px;
    padding: 3rem;
    text-align: center;
    background-color: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #0056b3;
    background-color: #e7f1ff;
}

.upload-area.dragover {
    border-color: #28a745;
    background-color: #e8f5e8;
}

.upload-icon {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

/* Progress Bar */
.progress {
    height: 25px;
    border-radius: 15px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 15px;
    font-weight: 600;
    line-height: 25px;
}

/* Document List */
.document-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
    transition: all 0.2s ease;
}

.document-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.document-title {
    font-weight: 600;
    color: #007bff;
    text-decoration: none;
    font-size: 1.1rem;
}

.document-title:hover {
    color: #0056b3;
    text-decoration: underline;
}

.document-meta {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.document-actions {
    margin-top: 1rem;
}

/* Classification Badge */
.classification-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 500;
}

.classification-academic {
    background-color: #e3f2fd;
    color: #1976d2;
}

.classification-business {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.classification-legal {
    background-color: #fff3e0;
    color: #f57c00;
}

.classification-technical {
    background-color: #e8f5e8;
    color: #388e3c;
}

.classification-medical {
    background-color: #ffebee;
    color: #d32f2f;
}

.classification-other {
    background-color: #f5f5f5;
    color: #616161;
}

/* Search Results */
.search-results {
    margin-top: 2rem;
}

.search-result-item {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.search-result-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.search-result-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.search-result-snippet {
    color: #495057;
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-card .stat-number {
        font-size: 2rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .upload-icon {
        font-size: 2rem;
    }
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Button Styles */
.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
}
