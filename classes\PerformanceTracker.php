<?php
/**
 * Performance Tracker Class
 * Tracks and analyzes system performance metrics
 */

class PerformanceTracker {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Track an operation's performance
     * @param string $operationType - Type of operation (upload, search, sort, classify)
     * @param float $executionTime - Time taken in seconds
     * @param int $recordsProcessed - Number of records processed
     * @param array $additionalData - Additional metadata
     * @return bool
     */
    public function trackOperation($operationType, $executionTime, $recordsProcessed = 1, $additionalData = []) {
        try {
            $sql = "INSERT INTO performance_metrics 
                    (operation_type, execution_time, records_processed, memory_usage, additional_data) 
                    VALUES (:operation_type, :execution_time, :records_processed, :memory_usage, :additional_data)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                ':operation_type' => $operationType,
                ':execution_time' => $executionTime,
                ':records_processed' => $recordsProcessed,
                ':memory_usage' => memory_get_peak_usage(true),
                ':additional_data' => json_encode($additionalData)
            ]);
            
        } catch (Exception $e) {
            error_log("Performance tracking error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get performance statistics for a specific operation type
     * @param string $operationType
     * @param int $days - Number of days to look back
     * @return array
     */
    public function getOperationStats($operationType, $days = 30) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_operations,
                        AVG(execution_time) as avg_execution_time,
                        MIN(execution_time) as min_execution_time,
                        MAX(execution_time) as max_execution_time,
                        SUM(records_processed) as total_records,
                        AVG(memory_usage) as avg_memory_usage,
                        MAX(memory_usage) as max_memory_usage
                    FROM performance_metrics 
                    WHERE operation_type = :operation_type 
                    AND timestamp >= DATE_SUB(NOW(), INTERVAL :days DAY)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':operation_type' => $operationType,
                ':days' => $days
            ]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Get operation stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get overall system performance statistics
     * @param int $days
     * @return array
     */
    public function getSystemStats($days = 30) {
        try {
            $sql = "SELECT 
                        operation_type,
                        COUNT(*) as total_operations,
                        AVG(execution_time) as avg_execution_time,
                        SUM(records_processed) as total_records,
                        AVG(memory_usage) as avg_memory_usage
                    FROM performance_metrics 
                    WHERE timestamp >= DATE_SUB(NOW(), INTERVAL :days DAY)
                    GROUP BY operation_type
                    ORDER BY total_operations DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':days' => $days]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get performance trends over time
     * @param string $operationType
     * @param int $days
     * @return array
     */
    public function getPerformanceTrends($operationType, $days = 30) {
        try {
            $sql = "SELECT 
                        DATE(timestamp) as date,
                        COUNT(*) as operations_count,
                        AVG(execution_time) as avg_execution_time,
                        SUM(records_processed) as records_processed
                    FROM performance_metrics 
                    WHERE operation_type = :operation_type 
                    AND timestamp >= DATE_SUB(NOW(), INTERVAL :days DAY)
                    GROUP BY DATE(timestamp)
                    ORDER BY date ASC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':operation_type' => $operationType,
                ':days' => $days
            ]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get slow operations (operations taking longer than threshold)
     * @param float $threshold - Time threshold in seconds
     * @param int $limit
     * @return array
     */
    public function getSlowOperations($threshold = 5.0, $limit = 50) {
        try {
            $sql = "SELECT 
                        operation_type,
                        execution_time,
                        records_processed,
                        memory_usage,
                        timestamp,
                        additional_data
                    FROM performance_metrics 
                    WHERE execution_time > :threshold
                    ORDER BY execution_time DESC
                    LIMIT :limit";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':threshold' => $threshold,
                ':limit' => $limit
            ]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get memory usage statistics
     * @param int $days
     * @return array
     */
    public function getMemoryStats($days = 30) {
        try {
            $sql = "SELECT 
                        operation_type,
                        AVG(memory_usage) as avg_memory,
                        MIN(memory_usage) as min_memory,
                        MAX(memory_usage) as max_memory,
                        COUNT(*) as operation_count
                    FROM performance_metrics 
                    WHERE timestamp >= DATE_SUB(NOW(), INTERVAL :days DAY)
                    GROUP BY operation_type
                    ORDER BY avg_memory DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':days' => $days]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Clean old performance data
     * @param int $daysToKeep
     * @return bool
     */
    public function cleanOldData($daysToKeep = 90) {
        try {
            $sql = "DELETE FROM performance_metrics 
                    WHERE timestamp < DATE_SUB(NOW(), INTERVAL :days DAY)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([':days' => $daysToKeep]);
            
        } catch (Exception $e) {
            error_log("Clean old data error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get performance summary for dashboard
     * @return array
     */
    public function getPerformanceSummary() {
        try {
            // Get recent performance data
            $sql = "SELECT 
                        operation_type,
                        COUNT(*) as count,
                        AVG(execution_time) as avg_time,
                        MAX(execution_time) as max_time
                    FROM performance_metrics 
                    WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    GROUP BY operation_type";
            
            $stmt = $this->db->query($sql);
            $recentStats = $stmt->fetchAll();
            
            // Get overall statistics
            $sql = "SELECT 
                        COUNT(*) as total_operations,
                        AVG(execution_time) as overall_avg_time,
                        MAX(execution_time) as overall_max_time,
                        AVG(memory_usage) as avg_memory_usage
                    FROM performance_metrics 
                    WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            
            $stmt = $this->db->query($sql);
            $overallStats = $stmt->fetch();
            
            return [
                'recent_stats' => $recentStats,
                'overall_stats' => $overallStats
            ];
            
        } catch (Exception $e) {
            return [
                'recent_stats' => [],
                'overall_stats' => []
            ];
        }
    }
    
    /**
     * Format execution time for display
     * @param float $time
     * @return string
     */
    public static function formatExecutionTime($time) {
        if ($time < 0.001) {
            return number_format($time * 1000000, 0) . ' μs';
        } elseif ($time < 1) {
            return number_format($time * 1000, 2) . ' ms';
        } else {
            return number_format($time, 3) . ' s';
        }
    }
    
    /**
     * Format memory usage for display
     * @param int $bytes
     * @return string
     */
    public static function formatMemoryUsage($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Get performance alerts (operations that are performing poorly)
     * @return array
     */
    public function getPerformanceAlerts() {
        $alerts = [];
        
        try {
            // Check for operations taking too long
            $sql = "SELECT operation_type, AVG(execution_time) as avg_time
                    FROM performance_metrics 
                    WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    GROUP BY operation_type
                    HAVING avg_time > 10"; // 10 seconds threshold
            
            $stmt = $this->db->query($sql);
            $slowOperations = $stmt->fetchAll();
            
            foreach ($slowOperations as $op) {
                $alerts[] = [
                    'type' => 'slow_operation',
                    'message' => "Operation '{$op['operation_type']}' is averaging " . 
                               self::formatExecutionTime($op['avg_time']) . " execution time",
                    'severity' => 'warning'
                ];
            }
            
            // Check for high memory usage
            $sql = "SELECT operation_type, AVG(memory_usage) as avg_memory
                    FROM performance_metrics 
                    WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    GROUP BY operation_type
                    HAVING avg_memory > 134217728"; // 128MB threshold
            
            $stmt = $this->db->query($sql);
            $highMemoryOps = $stmt->fetchAll();
            
            foreach ($highMemoryOps as $op) {
                $alerts[] = [
                    'type' => 'high_memory',
                    'message' => "Operation '{$op['operation_type']}' is using " . 
                               self::formatMemoryUsage($op['avg_memory']) . " memory on average",
                    'severity' => 'warning'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Performance alerts error: " . $e->getMessage());
        }
        
        return $alerts;
    }
    
    /**
     * Export performance data to CSV
     * @param string $operationType
     * @param int $days
     * @return string - CSV content
     */
    public function exportPerformanceData($operationType = null, $days = 30) {
        try {
            $sql = "SELECT operation_type, execution_time, records_processed, memory_usage, timestamp
                    FROM performance_metrics 
                    WHERE timestamp >= DATE_SUB(NOW(), INTERVAL :days DAY)";
            
            $params = [':days' => $days];
            
            if ($operationType) {
                $sql .= " AND operation_type = :operation_type";
                $params[':operation_type'] = $operationType;
            }
            
            $sql .= " ORDER BY timestamp DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $data = $stmt->fetchAll();
            
            // Generate CSV
            $csv = "Operation Type,Execution Time (s),Records Processed,Memory Usage (bytes),Timestamp\n";
            
            foreach ($data as $row) {
                $csv .= implode(',', [
                    $row['operation_type'],
                    $row['execution_time'],
                    $row['records_processed'],
                    $row['memory_usage'],
                    $row['timestamp']
                ]) . "\n";
            }
            
            return $csv;
            
        } catch (Exception $e) {
            error_log("Export performance data error: " . $e->getMessage());
            return '';
        }
    }
}
?>
