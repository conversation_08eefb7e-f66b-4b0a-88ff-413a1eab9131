<?php
/**
 * Document Manager Class
 * Handles document upload, storage, and metadata extraction
 */

require_once 'config/database.php';

class DocumentManager {
    private $db;
    private $performanceTracker;
    
    public function __construct($database) {
        $this->db = $database;
        $this->performanceTracker = new PerformanceTracker($database);
    }
    
    /**
     * Upload and process a document
     * @param array $file - $_FILES array element
     * @return array - Result with success status and message
     */
    public function uploadDocument($file) {
        $startTime = microtime(true);
        
        try {
            // Validate file
            $validation = $this->validateFile($file);
            if (!$validation['success']) {
                return $validation;
            }
            
            // Generate unique filename
            $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $uniqueFilename = uniqid('doc_') . '.' . $fileExtension;
            $uploadPath = Config::getUploadPath() . $uniqueFilename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
                return ['success' => false, 'message' => 'Failed to move uploaded file'];
            }
            
            // Extract document content and title
            $extractionResult = $this->extractDocumentContent($uploadPath, $fileExtension);
            
            // Save document metadata to database
            $documentId = $this->saveDocumentMetadata([
                'filename' => $uniqueFilename,
                'original_filename' => $file['name'],
                'title' => $extractionResult['title'],
                'file_path' => $uploadPath,
                'file_size' => $file['size'],
                'file_type' => $fileExtension,
                'content' => $extractionResult['content'],
                'extracted_text' => $extractionResult['text']
            ]);
            
            if ($documentId) {
                // Extract keywords for better search
                $this->extractAndSaveKeywords($documentId, $extractionResult['text']);
                
                // Auto-classify document
                $classificationEngine = new ClassificationEngine($this->db);
                $classification = $classificationEngine->classifyDocument($documentId);
                
                // Update statistics
                $this->updateStatistics($file['size']);
                
                $endTime = microtime(true);
                $this->performanceTracker->trackOperation('upload', $endTime - $startTime, 1);
                
                return [
                    'success' => true, 
                    'message' => 'Document uploaded successfully',
                    'document_id' => $documentId,
                    'classification' => $classification
                ];
            } else {
                // Clean up file if database save failed
                unlink($uploadPath);
                return ['success' => false, 'message' => 'Failed to save document metadata'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Upload error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Validate uploaded file
     * @param array $file
     * @return array
     */
    private function validateFile($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'File upload error: ' . $file['error']];
        }
        
        // Check file size
        if ($file['size'] > Config::MAX_FILE_SIZE) {
            return ['success' => false, 'message' => 'File size exceeds maximum allowed size'];
        }
        
        // Check file extension
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, Config::ALLOWED_EXTENSIONS)) {
            return ['success' => false, 'message' => 'File type not allowed'];
        }
        
        return ['success' => true];
    }
    
    /**
     * Extract content and title from document
     * @param string $filePath
     * @param string $fileType
     * @return array
     */
    private function extractDocumentContent($filePath, $fileType) {
        $content = '';
        $title = '';
        $text = '';
        
        try {
            switch ($fileType) {
                case 'pdf':
                    $result = $this->extractFromPDF($filePath);
                    break;
                case 'doc':
                case 'docx':
                    $result = $this->extractFromWord($filePath);
                    break;
                default:
                    $result = ['content' => '', 'title' => '', 'text' => ''];
            }
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'content' => '',
                'title' => pathinfo($filePath, PATHINFO_FILENAME),
                'text' => ''
            ];
        }
    }
    
    /**
     * Extract content from PDF file
     * @param string $filePath
     * @return array
     */
    private function extractFromPDF($filePath) {
        // For this implementation, we'll use a simple approach
        // In production, you might want to use libraries like TCPDF or similar
        
        // Try to extract text using pdftotext if available
        $text = '';
        $title = '';
        
        // Check if pdftotext is available
        if (shell_exec('which pdftotext')) {
            $output = shell_exec("pdftotext '$filePath' -");
            if ($output) {
                $text = $output;
                // Extract title from first line or filename
                $lines = explode("\n", trim($text));
                $title = !empty($lines[0]) ? trim($lines[0]) : pathinfo($filePath, PATHINFO_FILENAME);
            }
        }
        
        // Fallback: use filename as title
        if (empty($title)) {
            $title = pathinfo($filePath, PATHINFO_FILENAME);
        }
        
        return [
            'content' => $text,
            'title' => $title,
            'text' => $text
        ];
    }
    
    /**
     * Extract content from Word document
     * @param string $filePath
     * @return array
     */
    private function extractFromWord($filePath) {
        $text = '';
        $title = '';
        
        // For DOCX files, we can extract text from XML
        if (pathinfo($filePath, PATHINFO_EXTENSION) === 'docx') {
            $text = $this->extractFromDocx($filePath);
        }
        
        // Extract title from first line or use filename
        if (!empty($text)) {
            $lines = explode("\n", trim($text));
            $title = !empty($lines[0]) ? trim($lines[0]) : pathinfo($filePath, PATHINFO_FILENAME);
        } else {
            $title = pathinfo($filePath, PATHINFO_FILENAME);
        }
        
        return [
            'content' => $text,
            'title' => $title,
            'text' => $text
        ];
    }
    
    /**
     * Extract text from DOCX file
     * @param string $filePath
     * @return string
     */
    private function extractFromDocx($filePath) {
        $text = '';
        
        try {
            $zip = new ZipArchive();
            if ($zip->open($filePath) === TRUE) {
                $xml = $zip->getFromName('word/document.xml');
                if ($xml) {
                    $dom = new DOMDocument();
                    $dom->loadXML($xml);
                    $text = $dom->textContent;
                }
                $zip->close();
            }
        } catch (Exception $e) {
            // Fallback to filename
            $text = '';
        }
        
        return $text;
    }
    
    /**
     * Save document metadata to database
     * @param array $data
     * @return int|false
     */
    private function saveDocumentMetadata($data) {
        try {
            $sql = "INSERT INTO documents (filename, original_filename, title, file_path, file_size, file_type, content, extracted_text, status) 
                    VALUES (:filename, :original_filename, :title, :file_path, :file_size, :file_type, :content, :extracted_text, 'completed')";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':filename' => $data['filename'],
                ':original_filename' => $data['original_filename'],
                ':title' => $data['title'],
                ':file_path' => $data['file_path'],
                ':file_size' => $data['file_size'],
                ':file_type' => $data['file_type'],
                ':content' => $data['content'],
                ':extracted_text' => $data['extracted_text']
            ]);
            
            return $this->db->lastInsertId();
            
        } catch (PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Extract and save keywords from document text
     * @param int $documentId
     * @param string $text
     */
    private function extractAndSaveKeywords($documentId, $text) {
        // Simple keyword extraction (you can enhance this with NLP libraries)
        $words = str_word_count(strtolower($text), 1);
        $keywords = array_count_values($words);
        
        // Filter out common words and short words
        $stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'a', 'an'];
        
        foreach ($keywords as $word => $frequency) {
            if (strlen($word) > 3 && !in_array($word, $stopWords) && $frequency > 1) {
                $this->saveKeyword($documentId, $word, $frequency);
            }
        }
    }
    
    /**
     * Save keyword to database
     * @param int $documentId
     * @param string $keyword
     * @param int $frequency
     */
    private function saveKeyword($documentId, $keyword, $frequency) {
        try {
            $sql = "INSERT INTO document_keywords (document_id, keyword, frequency) 
                    VALUES (:document_id, :keyword, :frequency)
                    ON DUPLICATE KEY UPDATE frequency = frequency + :frequency";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':document_id' => $documentId,
                ':keyword' => $keyword,
                ':frequency' => $frequency
            ]);
        } catch (PDOException $e) {
            // Log error but don't fail the upload
            error_log("Keyword save error: " . $e->getMessage());
        }
    }
    
    /**
     * Update system statistics
     * @param int $fileSize
     */
    private function updateStatistics($fileSize) {
        try {
            // Update document count
            $sql = "UPDATE system_statistics SET stat_value = stat_value + 1 WHERE stat_name = 'total_documents'";
            $this->db->exec($sql);
            
            // Update total file size
            $sql = "UPDATE system_statistics SET stat_value = stat_value + :file_size WHERE stat_name = 'total_file_size'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':file_size' => $fileSize]);
            
        } catch (PDOException $e) {
            error_log("Statistics update error: " . $e->getMessage());
        }
    }
    
    /**
     * Get all documents with pagination
     * @param int $page
     * @param int $limit
     * @param string $sortBy
     * @param string $sortOrder
     * @return array
     */
    public function getDocuments($page = 1, $limit = 10, $sortBy = 'upload_date', $sortOrder = 'DESC') {
        $offset = ($page - 1) * $limit;
        
        $allowedSortFields = ['title', 'upload_date', 'file_size', 'classification'];
        $sortBy = in_array($sortBy, $allowedSortFields) ? $sortBy : 'upload_date';
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        
        try {
            $sql = "SELECT id, filename, original_filename, title, file_size, file_type, classification, 
                           classification_confidence, upload_date, status 
                    FROM documents 
                    WHERE status = 'completed'
                    ORDER BY $sortBy $sortOrder 
                    LIMIT :limit OFFSET :offset";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            return $stmt->fetchAll();
            
        } catch (PDOException $e) {
            error_log("Get documents error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get document by ID
     * @param int $id
     * @return array|false
     */
    public function getDocumentById($id) {
        try {
            $sql = "SELECT * FROM documents WHERE id = :id AND status = 'completed'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':id' => $id]);
            
            return $stmt->fetch();
            
        } catch (PDOException $e) {
            error_log("Get document error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete document
     * @param int $id
     * @return bool
     */
    public function deleteDocument($id) {
        try {
            // Get document info first
            $document = $this->getDocumentById($id);
            if (!$document) {
                return false;
            }
            
            // Delete file from filesystem
            if (file_exists($document['file_path'])) {
                unlink($document['file_path']);
            }
            
            // Delete from database
            $sql = "DELETE FROM documents WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([':id' => $id]);
            
            if ($result) {
                // Update statistics
                $sql = "UPDATE system_statistics SET stat_value = stat_value - 1 WHERE stat_name = 'total_documents'";
                $this->db->exec($sql);
                
                $sql = "UPDATE system_statistics SET stat_value = stat_value - :file_size WHERE stat_name = 'total_file_size'";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([':file_size' => $document['file_size']]);
            }
            
            return $result;
            
        } catch (PDOException $e) {
            error_log("Delete document error: " . $e->getMessage());
            return false;
        }
    }
}
?>
