<?php
/**
 * Classification Engine Class
 * Handles document classification using keyword-based and machine learning approaches
 */

class ClassificationEngine {
    private $db;
    private $performanceTracker;
    
    public function __construct($database) {
        $this->db = $database;
        $this->performanceTracker = new PerformanceTracker($database);
    }
    
    /**
     * Classify a document based on its content
     * @param int $documentId
     * @return array - Classification result with confidence score
     */
    public function classifyDocument($documentId) {
        $startTime = microtime(true);
        
        try {
            // Get document content
            $document = $this->getDocumentContent($documentId);
            if (!$document) {
                return ['category' => 'other', 'confidence' => 0.0];
            }
            
            // Combine title and content for classification
            $text = $document['title'] . ' ' . $document['extracted_text'];
            
            // Get classification using multiple methods
            $keywordClassification = $this->classifyByKeywords($text);
            $titleClassification = $this->classifyByTitle($document['title']);
            $patternClassification = $this->classifyByPatterns($text);
            
            // Combine results with weighted scoring
            $finalClassification = $this->combineClassifications([
                $keywordClassification,
                $titleClassification,
                $patternClassification
            ]);
            
            // Update document with classification
            $this->updateDocumentClassification($documentId, $finalClassification);
            
            // Track performance
            $endTime = microtime(true);
            $this->performanceTracker->trackOperation('classify', $endTime - $startTime, 1);
            
            // Update statistics
            $this->updateClassificationStatistics();
            
            return $finalClassification;
            
        } catch (Exception $e) {
            error_log("Classification error: " . $e->getMessage());
            return ['category' => 'other', 'confidence' => 0.0];
        }
    }
    
    /**
     * Get document content for classification
     * @param int $documentId
     * @return array|false
     */
    private function getDocumentContent($documentId) {
        try {
            $sql = "SELECT title, extracted_text, content FROM documents WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':id' => $documentId]);
            
            return $stmt->fetch();
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Classify document based on keywords
     * @param string $text
     * @return array
     */
    private function classifyByKeywords($text) {
        try {
            // Get classification rules
            $sql = "SELECT category, keywords, weight FROM classification_rules 
                    WHERE rule_type = 'keyword' AND is_active = 1";
            $stmt = $this->db->query($sql);
            $rules = $stmt->fetchAll();
            
            $scores = [];
            $text = strtolower($text);
            
            foreach ($rules as $rule) {
                $keywords = explode(',', strtolower($rule['keywords']));
                $score = 0;
                $matchedKeywords = 0;
                
                foreach ($keywords as $keyword) {
                    $keyword = trim($keyword);
                    if (!empty($keyword)) {
                        // Count occurrences of keyword
                        $count = substr_count($text, $keyword);
                        if ($count > 0) {
                            $score += $count * $rule['weight'];
                            $matchedKeywords++;
                        }
                    }
                }
                
                // Normalize score by number of keywords
                if ($matchedKeywords > 0) {
                    $scores[$rule['category']] = ($scores[$rule['category']] ?? 0) + 
                                                ($score * ($matchedKeywords / count($keywords)));
                }
            }
            
            if (empty($scores)) {
                return ['category' => 'other', 'confidence' => 0.0];
            }
            
            // Get category with highest score
            arsort($scores);
            $topCategory = array_key_first($scores);
            $maxScore = $scores[$topCategory];
            $totalScore = array_sum($scores);
            
            $confidence = $totalScore > 0 ? min(($maxScore / $totalScore) * 100, 100) : 0;
            
            return [
                'category' => $topCategory,
                'confidence' => round($confidence, 2),
                'method' => 'keyword',
                'scores' => $scores
            ];
            
        } catch (Exception $e) {
            return ['category' => 'other', 'confidence' => 0.0];
        }
    }
    
    /**
     * Classify document based on title patterns
     * @param string $title
     * @return array
     */
    private function classifyByTitle($title) {
        try {
            $sql = "SELECT category, keywords, weight FROM classification_rules 
                    WHERE rule_type = 'title' AND is_active = 1";
            $stmt = $this->db->query($sql);
            $rules = $stmt->fetchAll();
            
            $scores = [];
            $title = strtolower($title);
            
            foreach ($rules as $rule) {
                $patterns = explode(',', strtolower($rule['keywords']));
                $score = 0;
                
                foreach ($patterns as $pattern) {
                    $pattern = trim($pattern);
                    if (!empty($pattern) && strpos($title, $pattern) !== false) {
                        $score += $rule['weight'];
                    }
                }
                
                if ($score > 0) {
                    $scores[$rule['category']] = ($scores[$rule['category']] ?? 0) + $score;
                }
            }
            
            if (empty($scores)) {
                return ['category' => 'other', 'confidence' => 0.0];
            }
            
            arsort($scores);
            $topCategory = array_key_first($scores);
            $confidence = min(($scores[$topCategory] / array_sum($scores)) * 100, 100);
            
            return [
                'category' => $topCategory,
                'confidence' => round($confidence, 2),
                'method' => 'title'
            ];
            
        } catch (Exception $e) {
            return ['category' => 'other', 'confidence' => 0.0];
        }
    }
    
    /**
     * Classify document based on text patterns
     * @param string $text
     * @return array
     */
    private function classifyByPatterns($text) {
        // Define pattern-based classification rules
        $patterns = [
            'academic' => [
                '/\b(abstract|introduction|methodology|results|conclusion|references|bibliography)\b/i',
                '/\b(research|study|analysis|experiment|hypothesis|data|findings)\b/i',
                '/\b(university|college|journal|paper|thesis|dissertation)\b/i'
            ],
            'legal' => [
                '/\b(whereas|therefore|hereby|herein|party|parties|agreement|contract)\b/i',
                '/\b(law|legal|court|judge|attorney|lawyer|case|statute)\b/i',
                '/\b(terms|conditions|liability|damages|breach|compliance)\b/i'
            ],
            'business' => [
                '/\b(revenue|profit|loss|financial|budget|investment|roi)\b/i',
                '/\b(company|corporation|business|enterprise|organization)\b/i',
                '/\b(strategy|market|customer|sales|marketing|proposal)\b/i'
            ],
            'technical' => [
                '/\b(system|software|hardware|technology|programming|development)\b/i',
                '/\b(algorithm|database|network|server|application|interface)\b/i',
                '/\b(technical|specification|manual|documentation|api)\b/i'
            ],
            'medical' => [
                '/\b(patient|doctor|physician|nurse|hospital|clinic|medical)\b/i',
                '/\b(diagnosis|treatment|therapy|medication|surgery|symptoms)\b/i',
                '/\b(health|disease|condition|disorder|syndrome|clinical)\b/i'
            ]
        ];
        
        $scores = [];
        $text = strtolower($text);
        
        foreach ($patterns as $category => $categoryPatterns) {
            $score = 0;
            foreach ($categoryPatterns as $pattern) {
                $matches = preg_match_all($pattern, $text);
                $score += $matches;
            }
            
            if ($score > 0) {
                $scores[$category] = $score;
            }
        }
        
        if (empty($scores)) {
            return ['category' => 'other', 'confidence' => 0.0];
        }
        
        arsort($scores);
        $topCategory = array_key_first($scores);
        $confidence = min(($scores[$topCategory] / array_sum($scores)) * 100, 100);
        
        return [
            'category' => $topCategory,
            'confidence' => round($confidence, 2),
            'method' => 'pattern'
        ];
    }
    
    /**
     * Combine multiple classification results
     * @param array $classifications
     * @return array
     */
    private function combineClassifications($classifications) {
        $combinedScores = [];
        $weights = [
            'keyword' => 0.5,
            'title' => 0.3,
            'pattern' => 0.2
        ];
        
        foreach ($classifications as $classification) {
            if ($classification['category'] !== 'other' && $classification['confidence'] > 0) {
                $category = $classification['category'];
                $method = $classification['method'] ?? 'keyword';
                $weight = $weights[$method] ?? 0.3;
                
                $weightedScore = $classification['confidence'] * $weight;
                $combinedScores[$category] = ($combinedScores[$category] ?? 0) + $weightedScore;
            }
        }
        
        if (empty($combinedScores)) {
            return ['category' => 'other', 'confidence' => 0.0];
        }
        
        // Normalize scores
        $maxScore = max($combinedScores);
        $totalScore = array_sum($combinedScores);
        
        arsort($combinedScores);
        $topCategory = array_key_first($combinedScores);
        
        // Calculate final confidence
        $confidence = $totalScore > 0 ? min(($maxScore / $totalScore) * 100, 100) : 0;
        
        // Apply minimum confidence threshold
        if ($confidence < 30) {
            return ['category' => 'other', 'confidence' => round($confidence, 2)];
        }
        
        return [
            'category' => $topCategory,
            'confidence' => round($confidence, 2)
        ];
    }
    
    /**
     * Update document classification in database
     * @param int $documentId
     * @param array $classification
     */
    private function updateDocumentClassification($documentId, $classification) {
        try {
            $sql = "UPDATE documents 
                    SET classification = :category, classification_confidence = :confidence 
                    WHERE id = :id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':category' => $classification['category'],
                ':confidence' => $classification['confidence'],
                ':id' => $documentId
            ]);
        } catch (Exception $e) {
            error_log("Classification update error: " . $e->getMessage());
        }
    }
    
    /**
     * Update classification statistics
     */
    private function updateClassificationStatistics() {
        try {
            $sql = "UPDATE system_statistics SET stat_value = stat_value + 1 WHERE stat_name = 'total_classifications'";
            $this->db->exec($sql);
        } catch (Exception $e) {
            error_log("Classification statistics error: " . $e->getMessage());
        }
    }
    
    /**
     * Classify multiple documents in batch
     * @param array $documentIds
     * @return array
     */
    public function batchClassifyDocuments($documentIds) {
        $startTime = microtime(true);
        $results = [];
        
        foreach ($documentIds as $documentId) {
            $result = $this->classifyDocument($documentId);
            $results[$documentId] = $result;
        }
        
        $endTime = microtime(true);
        $this->performanceTracker->trackOperation('batch_classify', $endTime - $startTime, count($documentIds));
        
        return $results;
    }
    
    /**
     * Get classification statistics
     * @return array
     */
    public function getClassificationStatistics() {
        try {
            $sql = "SELECT classification, COUNT(*) as count, 
                           AVG(classification_confidence) as avg_confidence,
                           MIN(classification_confidence) as min_confidence,
                           MAX(classification_confidence) as max_confidence
                    FROM documents 
                    WHERE status = 'completed' 
                    GROUP BY classification 
                    ORDER BY count DESC";
            
            $stmt = $this->db->query($sql);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Add new classification rule
     * @param string $category
     * @param string $keywords
     * @param float $weight
     * @param string $ruleType
     * @return bool
     */
    public function addClassificationRule($category, $keywords, $weight = 1.0, $ruleType = 'keyword') {
        try {
            $sql = "INSERT INTO classification_rules (category, keywords, weight, rule_type) 
                    VALUES (:category, :keywords, :weight, :rule_type)";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                ':category' => $category,
                ':keywords' => $keywords,
                ':weight' => $weight,
                ':rule_type' => $ruleType
            ]);
            
        } catch (Exception $e) {
            error_log("Add classification rule error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all classification rules
     * @return array
     */
    public function getClassificationRules() {
        try {
            $sql = "SELECT * FROM classification_rules ORDER BY category, rule_type";
            $stmt = $this->db->query($sql);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Update classification rule
     * @param int $ruleId
     * @param array $data
     * @return bool
     */
    public function updateClassificationRule($ruleId, $data) {
        try {
            $sql = "UPDATE classification_rules 
                    SET category = :category, keywords = :keywords, weight = :weight, 
                        rule_type = :rule_type, is_active = :is_active 
                    WHERE id = :id";
            
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([
                ':category' => $data['category'],
                ':keywords' => $data['keywords'],
                ':weight' => $data['weight'],
                ':rule_type' => $data['rule_type'],
                ':is_active' => $data['is_active'] ? 1 : 0,
                ':id' => $ruleId
            ]);
            
        } catch (Exception $e) {
            error_log("Update classification rule error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete classification rule
     * @param int $ruleId
     * @return bool
     */
    public function deleteClassificationRule($ruleId) {
        try {
            $sql = "DELETE FROM classification_rules WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute([':id' => $ruleId]);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Reclassify all documents
     * @return array
     */
    public function reclassifyAllDocuments() {
        try {
            $sql = "SELECT id FROM documents WHERE status = 'completed'";
            $stmt = $this->db->query($sql);
            $documentIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            return $this->batchClassifyDocuments($documentIds);
            
        } catch (Exception $e) {
            error_log("Reclassify all error: " . $e->getMessage());
            return [];
        }
    }
}
?>
