# Cloud-Based Document Analytics System

A comprehensive PHP and MySQL-based document management system for searching, sorting, and classifying PDF and Word documents with cloud deployment capabilities.

## 🚀 Features

### Core Functionality
- **Document Upload & Storage**: Support for PDF, DOC, and DOCX files with automatic text extraction
- **Advanced Search**: Full-text search with keyword highlighting and performance tracking
- **Document Sorting**: Sort by title, date, size, classification, and file type
- **Auto-Classification**: Intelligent document classification using keyword-based algorithms
- **Performance Analytics**: Comprehensive statistics and performance monitoring

### Technical Features
- **Responsive Web Interface**: Bootstrap-based UI that works on all devices
- **RESTful API**: Clean API endpoints for all operations
- **Performance Tracking**: Real-time monitoring of system performance
- **Search Suggestions**: Auto-complete search functionality
- **File Management**: Secure file upload and storage
- **Database Optimization**: Indexed searches and optimized queries

## 📋 Requirements

### System Requirements
- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher
- **Web Server**: Apache or Nginx
- **Extensions**: PDO, ZIP, GD (optional)

### Cloud Platform Support
- AWS (EC2, RDS, S3)
- Google Cloud Platform
- Microsoft Azure
- Shared hosting providers

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/document-analytics-system.git
cd document-analytics-system
```

### 2. Database Setup
```sql
-- Import the database schema
mysql -u username -p database_name < database/schema.sql
```

### 3. Configuration
Edit `config/database.php` with your database credentials:
```php
private $host = 'localhost';
private $db_name = 'document_analytics';
private $username = 'your_username';
private $password = 'your_password';
```

### 4. File Permissions
```bash
chmod 755 uploads/
chmod 755 uploads/documents/
```

### 5. Web Server Configuration

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]
```

#### Nginx
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}
```

## 🏗️ Architecture

### Directory Structure
```
document-analytics-system/
├── api/                    # API endpoints
├── assets/                 # CSS, JS, images
├── classes/               # PHP classes
├── config/                # Configuration files
├── database/              # Database schema
├── pages/                 # Page templates
├── uploads/               # Document storage
└── index.php             # Main entry point
```

### Core Classes
- **DocumentManager**: Handles file uploads and document operations
- **SearchEngine**: Manages search functionality and indexing
- **ClassificationEngine**: Implements document classification algorithms
- **PerformanceTracker**: Monitors and tracks system performance

## 🔍 Classification Algorithm

The system uses a multi-layered classification approach:

1. **Keyword Matching**: Documents analyzed for category-specific keywords
2. **Title Analysis**: Document titles examined for classification patterns
3. **Pattern Recognition**: Text patterns specific to document types
4. **Weighted Scoring**: Combined confidence scoring from all methods
5. **Threshold Application**: Documents below 30% confidence marked as "other"

### Classification Categories
- Academic Papers
- Business Documents
- Legal Documents
- Technical Documentation
- Medical Documents
- Other Documents

## 📊 Performance Features

### Metrics Tracked
- Upload performance and file processing times
- Search execution times and result counts
- Classification accuracy and processing speed
- Memory usage and system resource utilization

### Statistics Available
- Document count and storage usage
- Search frequency and popular queries
- Classification distribution
- Performance trends over time

## 🔧 API Endpoints

### Document Operations
- `POST /api/upload.php` - Upload documents
- `GET /api/documents.php` - List documents
- `DELETE /api/delete_document.php` - Delete document

### Search Operations
- `GET /api/search.php` - Search documents
- `GET /api/search_suggestions.php` - Get search suggestions

### Export Operations
- `GET /api/export_performance.php` - Export performance data
- `GET /api/export_documents.php` - Export document list

## 🌐 Cloud Deployment

### AWS Deployment
1. **EC2 Instance**: Launch Ubuntu/Amazon Linux instance
2. **RDS Database**: Create MySQL database instance
3. **S3 Storage**: Optional for document storage
4. **Load Balancer**: For high availability

### Google Cloud Deployment
1. **Compute Engine**: Deploy on VM instance
2. **Cloud SQL**: MySQL database service
3. **Cloud Storage**: For document files
4. **Load Balancer**: For scaling

### Configuration for Cloud
Update `config/database.php` for cloud database:
```php
private $host = 'your-cloud-db-host';
private $db_name = 'document_analytics';
private $username = 'cloud_user';
private $password = 'secure_password';
```

## 🔒 Security Features

- **File Validation**: Strict file type and size checking
- **SQL Injection Protection**: Prepared statements throughout
- **XSS Prevention**: Input sanitization and output escaping
- **CSRF Protection**: Form token validation
- **Secure File Storage**: Files stored outside web root

## 📈 Performance Optimization

### Database Optimization
- Full-text indexes on content fields
- Composite indexes for common queries
- Query optimization and caching

### File Processing
- Asynchronous document processing
- Memory-efficient text extraction
- Chunked file uploads for large files

### Caching Strategy
- Database query result caching
- Static asset caching
- Browser caching headers

## 🧪 Testing

### Running Tests
```bash
# Unit tests
php tests/unit/DocumentManagerTest.php

# Integration tests
php tests/integration/SearchEngineTest.php

# Performance tests
php tests/performance/LoadTest.php
```

### Test Coverage
- Document upload and processing
- Search functionality and accuracy
- Classification algorithm performance
- API endpoint responses

## 📚 Usage Examples

### Upload Documents
```php
$documentManager = new DocumentManager($db);
$result = $documentManager->uploadDocument($_FILES['document']);
```

### Search Documents
```php
$searchEngine = new SearchEngine($db);
$results = $searchEngine->searchDocuments('keyword', $filters, $page, $limit);
```

### Classify Document
```php
$classificationEngine = new ClassificationEngine($db);
$classification = $classificationEngine->classifyDocument($documentId);
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-feature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/new-feature`)
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Authors

- **Your Name** - *Initial work* - [YourGitHub](https://github.com/yourusername)

## 🙏 Acknowledgments

- Islamic University of Gaza - Faculty of Information Technology
- Cloud and Distributed Systems Course (SICT 4313)
- Bootstrap for UI components
- Chart.js for data visualization

## 📞 Support

For support and questions:
- Email: <EMAIL>
- GitHub Issues: [Create an issue](https://github.com/yourusername/document-analytics-system/issues)

## 🔄 Version History

- **v1.0.0** - Initial release with core functionality
- **v1.1.0** - Added performance tracking and statistics
- **v1.2.0** - Enhanced classification algorithms
- **v1.3.0** - Cloud deployment optimizations

---

**Note**: This system was developed as part of the Cloud and Distributed Systems course at the Islamic University of Gaza. It demonstrates practical implementation of cloud-based document analytics using PHP and MySQL.
